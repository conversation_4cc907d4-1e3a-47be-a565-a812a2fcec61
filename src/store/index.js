import Vue from 'vue'
import Vuex from 'vuex'
// import { getMatedateAll} from "../api/first";
Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    tagsList: [],
    data:[],
    name:''
  },
  mutations: {
    getData(state,all){
      // debugger
      state.data = all
      console.log(all,'all')
    },
    getName(state,all){
      // debugger
      state.name = all
      // console.log(all,'all')
    },
  },
  actions: {
  },
  modules: {
  }
})

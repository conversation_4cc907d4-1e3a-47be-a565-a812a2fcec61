import axios from "axios";
import {Message,Loading} from 'element-ui'
const service = axios.create({
    // process.env.NODE_ENV === 'development' 来判断是否开发环境
    // easy-mock服务挂了，暂时不使用了
    // baseURL: 'https://www.easy-mock.com/mock/592501a391470c0ac1fab128',
    timeout: 100000,
});
if (process.env.NODE_ENV === "development") {
    service.defaults.baseURL = "http://localhost:8085/api";
    // service.defaults.baseURL = process.env.VUE_APP_BASE_WEBAPI;
} else {
    //测试
    // service.defaults.baseURL='/business-test'
    //正式
    service.defaults.baseURL = process.env.VUE_APP_BASE_API;
}
// service.defaults.baseURL= process.env.VUE_APP_BASE_API;
service.interceptors.request.use(
    (config) => {
        var token = localStorage.getItem("Authorization");
        if (token) {
            // config.headers["Authorization"] = "Bearer " + token;
            config.headers["Authorization"] = token;
        } else {
            /*if (process.env.NODE_ENV == "development") {
                location.href =
                    "http://*************/eams/sso.html?clientId=dzdazhlyzxt&redirectUri=" +
                    encodeURI("http://localhost:8080");
            } else {
                location.href =
                    "http://*************/eams/sso.html?clientId=dzdazhlyzxt&redirectUri=" +
                    encodeURI("http://*************:8080/business-web");
            }
            //将参数放在URLSearchParams函数中
            localStorage.setItem("code", param.get("code"));*/

        }
        return config;
    },
    (error) => {
        console.log(error);
        return Promise.reject();
    }
);
service.interceptors.response.use(
    (response) => {
        if (response.status === 200) {
            if (response.data.code === 401){
                Loading.service({
                    text:response.data.msg,
                    fullscreen:true
                });
            }
            return response.data;
        } else {
            Promise.reject().then(r => {});
        }


    },
    (error) => {
        console.log(error);
        return Promise.reject();
    }
);

export default service;

import dictList from '@/utils/dict'

/**获取静态字典值 **/
export function getDict(data, element, dictName) {
    const col = element.property
    const dictArr = dictList[dictName]
    if (!dictArr) return
    for (let item of dictArr) {
        if (item.id === data[col]) {
            return item.name
        }
    }
}

/**获取静态字典列表 **/
export function getDictList(dictName) {
    return dictList[dictName]
}


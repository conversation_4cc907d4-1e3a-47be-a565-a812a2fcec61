function formatDate(value) {
    var date = new Date(value);
    var y = date.getFullYear(),
        m = date.getMonth() + 1,
        d = date.getDate(),
        h = date.getHours(),
        i = date.getMinutes(),
        s = date.getSeconds();
    if (m < 10) {
        m = '0' + m;
    }
    if (d < 10) {
        d = '0' + d;
    }
    if (h < 10) {
        h = '0' + h;
    }
    if (i < 10) {
        i = '0' + i;
    }
    if (s < 10) {
        s = '0' + s;
    }
    var t = y + '-' + m + '-' + d + ' ' + h + ':' + i + ':' + s;
    return t;
}

const getObjectKeys=(object)=>
  {
    var keys = [];
    for (var property in object)
      keys.push(property);
    return keys;
  }
 
  const getObjectValues=(object)=>
  {
    var values = [];
    for (var property in object)
      values.push(object[property]);
    return values;
  }
export const tool={
    getObjectKeys,
    getObjectValues,
    formatDate
}
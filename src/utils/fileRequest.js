import axios from "axios";

const service = axios.create({
  // process.env.NODE_ENV === 'development' 来判断是否开发环境
  // easy-mock服务挂了，暂时不使用了
  // baseURL: 'https://www.easy-mock.com/mock/592501a391470c0ac1fab128',
  timeout: 100000,
});
if (process.env.NODE_ENV === "development") {
  service.defaults.baseURL = "http://19.104.55.186/api";
} else {
    //代理186服务器
  // service.defaults.baseURL = "http://19.104.55.186/api";
    //测试服
  service.defaults.baseURL = process.env.VUE_APP_FILE_BASE_API;
}
// service.defaults.baseURL= process.env.VUE_APP_BASE_API;
service.interceptors.request.use(
  (config) => {
    let token = sessionStorage.getItem("token");
    if (token) {
      config.headers["Authorization"] = "Bearer " + token;
    }
    return config;
  },
  (error) => {
    console.log(error);
    return Promise.reject();
  }
);

service.interceptors.response.use(
  (response) => {
    if (response.status === 200) {
      return response.data;
    } else {
      Promise.reject();
    }
  },
  (error) => {
    console.log(error);
    return Promise.reject();
  }
);

export default service;

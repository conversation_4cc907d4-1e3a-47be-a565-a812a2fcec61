const dictList = {
    paperDealNode: [
        {id: '3', name: "申办"},
        {id: '4', name: '网上预受理'},
        {id: '5', name: '受理'},
        {id: '6-1', name: '承办'},
        {id: '6-2', name: '审核'},
        {id: '6-3', name: '批准'},
        {id: '7', name: '补正告知'},
        {id: '8', name: '补正受理'},
        {id: '9', name: '特别程序申请'},
        {id: '10', name: '特别程序结果'},
        {id: '11', name: '办结'},
        {id: '11-1', name: '不通过办结'},
        {id: '12', name: '制证'},
        {id: '13', name: '领取登记'},
    ],
    paperDealAction: [
        {id: '1', name: '通过'},
        {id: '2', name: '不通过'},
        {id: '3', name: '退回'},
        {id: '4', name: '补正告知'},
        {id: '9', name: '其他'}
    ],
    paperDealType: [
        {id: '1', name: '通知书'},
        {id: '2', name: '文书'},
        {id: '3', name: '证件'}
    ],
    taskType: [
        {id: '01', name: '行政许可'},
        {id: '02', name: '行政处罚'},
        {id: '03', name: '行政强制'},
        {id: '04', name: '行政征收'},
        {id: '05', name: '行政给付'},
        {id: '06', name: '行政检查'},
        {id: '07', name: '行政确认'},
        {id: '08', name: '行政奖励'},
        {id: '09', name: '行政裁决'},
        {id: '20', name: '其他行政权力'},
        {id: '21', name: '公共服务'},
    ],
    //收据类型
    /**
     * 回执类型 0.网上申请回执 1.不受理回执(即：不收件(通知书)) 2.材料告知（即：1015申请材料告知书） 3.受理回执 4.物料回执 5.收费通知,6.行政确认收件,9收件通知书(内部),10不动产跳转URL,11.送达回证 12物料流转清单 .13材料补正凭证，14网上申报收件凭证,20广东省冷藏冷冻食品贮存服务提供者备案注销凭据,21广东省冷藏冷冻食品贮存服务提供者备案凭据
     * **/
    receiptType: [
        {id: '0', name: '网上申请回执'},
        {id: '1', name: '不收件(通知书)'},
        {id: '2', name: '材料告知'},
        {id: '3', name: '受理回执'},
        {id: '4', name: '物料回执'},
        {id: '5', name: '收费通知'},
        {id: '6', name: '行政确认收件'},
        {id: '9', name: '收件通知书(内部)'},
        {id: '10', name: '不动产跳转URL'},
        {id: '11', name: '送达回证'},
        {id: '12', name: '物料流转清单'},
        {id: '13', name: '材料补正凭证'},
        {id: '14', name: '网上申报收件凭证'},
        {id: '20', name: '广东省冷藏冷冻食品贮存服务提供者备案注销凭据'},
        {id: '21', name: '广东省冷藏冷冻食品贮存服务提供者备案凭据'},
    ],
}
export default dictList


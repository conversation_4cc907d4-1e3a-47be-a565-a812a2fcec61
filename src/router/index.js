import Vue from "vue";
import VueRouter from "vue-router";
import Home from "../views/Home.vue";
import {authApi} from "../api/auth";

Vue.use(VueRouter);
// 获取原型对象上的push函数
const originalPush = VueRouter.prototype.push
// 修改原型对象中的push方法
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

const routes = [
    {
        path: "/",
        name: "Home",
        component: Home,
        children: [],
    },
    {
        path: "/login",
        name: "Login",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(/* webpackChunkName: "about" */ "../views/Login.vue");
        },
    },
    {
        path: "/dashboard",
        name: "dashboard",
        meta: {
            title: "首页",
        },
        component: () => import("../views/Desktop.vue"),
    }, {
        path: "/signatureManage",
        name: "signatureManage",
        component: () => import("../views/SignatureManage.vue"),
    },
    {
        path: "/departmentNo",
        name: "DepartmentNo",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(
                /* webpackChunkName: "about" */ "../views/DepartmentNo.vue"
                );
        },
    },
    {
        path: "/department",
        name: "Department",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(/* webpackChunkName: "about" */ "../views/Department.vue");
        },

    },
    {
        path: "/about",
        name: "About",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(/* webpackChunkName: "about" */ "../views/About.vue");
        },
    },
    {
        path: "/progressQuery",
        name: "ProgressQuery",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(
                /* webpackChunkName: "about" */ "../views/ProgressQuery.vue"
                );
        },

    },
    {
        path: "/archivesType",
        name: "ArchivesType",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(
                /* webpackChunkName: "about" */ "../views/ArchivesType.vue"
                );
        },
    },
    {
        path: "/term",
        name: "Term",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(/* webpackChunkName: "about" */ "../views/Term.vue");
        },
    },
    {
        path: "/collectionLog",
        name: "CollectionLog",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(
                /* webpackChunkName: "about" */ "../views/CollectionLog.vue"
                );
        },
    },
    {
        path: "/sync",
        name: "Sync",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(/* webpackChunkName: "about" */ "../views/Sync.vue");
        },
    },
    {
        path: "/ready",
        name: "Ready",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(
                /* webpackChunkName: "about" */ "../views/ReadyFileInfo.vue"
                );
        },
    },
    {
        path: "/solidify",
        name: "Solidify",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(/* webpackChunkName: "about" */ "../views/Solidify.vue");
        },
    },
    {
        path: "/degree",
        name: "Degree",
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: function () {
            return import(/* webpackChunkName: "about" */ "../views/Degree.vue");
        },
    },
    {
        path: "/xmlManagement",
        name: "xmlManagement",
        component: () => import("../views/XmlManagement.vue"),
    },
    {
        path: "/metadataManagement",
        name: "metadataManagement",
        component: () => import("../views/MetadataManagement.vue"),
        meta: {
            keepAlive: true // 需要缓存false//不需要缓存
          }
    },
    {
        path: "/packetNamingManagement",
        name: "packetNamingManagement",
        component: () => import("../views/PacketNamingManagement.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/categoryNumberManagement",
        name: "packetNamingManagement",
        component: () => import("../views/CategoryNumberManagement.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/electronicPageNumberManagement",
        name: "electronicPageNumberManagement",
        component: () => import("../views/ElectronicPageNumberManagement.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/archivesNumberManagement",
        name: "archivesNumberManagement",
        component: () => import("../views/ArchivesNumberManagement.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/fileNumberManagement",
        name: "fileNumberManagement",
        component: () => import("../views/FileNumberManagement.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/encodingNumberManagement",
        name: "encodingNumberManagement",
        component: () => import("../views/EncodingNumberManagement.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/encodingNumberManagement",
        name: "encodingNumberManagement",
        component: () => import("../views/EncodingNumberManagement.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/fourTestManagement",
        name: "fourTestManagement",
        component: () => import("../views/FourTestManagement.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/fileCount",
        name: "fileCount",
        component: () => import("../views/FileCount.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/first",
        name: "first",
        component: () => import("../views/first.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/second",
        name: "second",
        component: () => import("../views/second.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/third",
        name: "third",
        component: () => import("../views/third.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/fourth",
        name: "fourth",
        component: () => import("../views/fourth.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/selects",
        name: "selects",
        component: () => import("../views/Selects.vue"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/departmentSignature",
        name: "departmentSignature",
        component: () => import("../views/DepartmentSignature"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    }, {
        path: "/redirectPage",
        name: "redirectPage",
        component: () => import("../views/RedirectPage"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },{
        path: "/fourCheckedOptions",
        name: "fourCheckedOptions",
        component: () => import("../views/FourCheckedOptions"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    ,{
        path: "/collection",
        name: "collection",
        component: () => import("../views/Collection"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
    {
        path: "/deptTree",
        name: "DeptTree",
        component: () => import("../views/About"),
        meta: {
            keepAlive: false //此页面不需要缓存
        }
    },
];

const router = new VueRouter({
    mode: "history",
    base: process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test' ? '/business-web/' : '/',
    routes,
});
// router.beforeEach((to,from,next)=>{
//   let token=sessionStorage.getItem("token");
//   //不存在token则请求
//   if(!token){
//     authApi.auth();
//     setTimeout(a=>{

//       let token2=sessionStorage.getItem("token");

//       next();
//     },300)
//   }
//   next();
// })
export default router;

<template>
  <div class="zipHome" id="zipHome">
    <el-radio-group
      v-model="isCollapse"
      @change="showOrHide"
      size="mini"
      style="margin-bottom: 20px"
    >
      <el-radio-button :label="true">展开</el-radio-button>
      <el-radio-button :label="false">收起</el-radio-button>
    </el-radio-group>
    <div class="contentDiv">
      <div class="fitst" ref="fitst" id="firstDiv">
        {{
          
          noNum
        }}
        <div
          class="fitst"
          v-for="(item, index) in list"
          :ref="`first` + index"
          :id="`twoDiv-` + index"
        >
          {{ item.label }}
          <div
            class="fitst"
            v-for="(item2, index2) in item.childs"
            :ref="`first` + index + `-child-` + index2"
            :id="`threeDiv-` + index2"
          >
            {{ item2.label }}
            <div
              class="fitst"
              :id="`foreDiv-` + index2 + `-` + index3"
              v-for="(item3, index3) in item2.childs"
              :ref="`first` + index + `-child` + index2 + `-child` + index3"
            >
              {{ item3.label }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LeaderLine from "leader-line";
export default {
  components: {},
  props:{
    noNum:{
      type:String,
      default:()=>""
    },
    list:{
      type:Array,
      default:()=>[]
    }
  },
  mounted() {
    setTimeout(a=>{
      this.initDivPointer();
      this.initLine();
      this.showLine();
      this.registerEvent();
    },1000)
    
  },
  computed: {},
  data() {
    return {
      divRefs: [],
      isCollapse: true,
      line: [],
      //保留每个div的高度
      tempIndex2: [],
      tempChildList: [],
      
      styleOption: {
        color: "blue", // 指引线颜色
        endPlug: "disc", // 指引线结束点的样式 hand,disc
        size: 1, // 线条尺寸
        startSocket: "right", //在指引线开始的地方从元素左侧开始
        endSocket: "left", //在指引线开始的地方从元素右侧结束
        hide: false, // 绘制时隐藏，默认为false，在初始化时可能会出现闪烁的线条
        startPlugColor: "black", // 渐变色开始色
        endPlugColor: "black", // 渐变色结束色
        gradient: true, // 使用渐变色
        outLineColor: "blue",
        path: "grid", // straight,arc,fluid,magnet,grid
        // dash: {
        //   // 虚线样式
        //   animation: false, // 让线条滚动起来
        // },
        zIndex: 9999,
        hide: false,
      },
      //下一个坐标值
      nextCoordinate: 0,
      lineContainerTemp: [],
      showEffectName: "draw",
      animOptions: {
        duration: 1000, //持续时长
        timing: "ease-in", // 动画函数
      },
    };
  },
  methods: {
    showOrHide(val) { 
      this.isCollapse = val;
      this.tempIndex2 = [];
      this.nextCoordinate = 0;
      this.divRefs.forEach((a) => {
        // this.$refs[a][0].style.top='0px';
        this.$refs[a][0].style.left = "0px";
      });

      this.line.forEach((c) => {
        c.remove();
      });
      this.line = [];
      this.initDivPointer();
      this.initLine();
      this.showLine();
    },
    //初始化div的位置
    initDivPointer() { 
      let temp = [];
      for (let index = 0; index < this.list.length; index++) {
        let name = `first` + index;
        this.divRefs.push(name);
        if (!this.list[index].childs || this.list[index].childs.length <= 0) {
          this.list[index].isCollapse = false;
        }
        let label = this.list[index].label;

        this.setBackGroundColor(label, name, this.list[index].childs);

        this.$refs[name][0].style.left = 300 + "px";
        if (temp.length > 0) {
          let tempHeight = 0;
          temp.forEach((s) => {
            tempHeight += s + 20;
          });
          this.$refs[name][0].style.top = tempHeight + "px";
        }
        let height = window
          .getComputedStyle(this.$refs[name][0])
          .height.split("px")[0];
        temp.push(parseInt(height));
        let tempHegiht = 0;
        //第二层
        //记录最后子集高度
        let lastChildIndex = 0;
        if (this.list[index].childs && this.list[index].childs.length > 0) {
          for (
            let index2 = 0;
            index2 < this.list[index].childs.length;
            index2++
          ) {
            //获取第二层ref的名称
            let name2 = `first` + index + `-child-` + index2;

            this.setBackGroundColor(
              this.list[index].childs[index2].label,
              name2,
              this.list[index].childs[index2].childs
            );

            //上一个子集
            let preData = [];
            //上一个子集的高度
            let childHeight = 0;
            if (index2 > 0) {
              preData = this.list[index].childs[index2 - 1].childs;
              if (this.isCollapse == false) {
                if (preData && preData.length > 0) {
                  lastChildIndex = index2 - 1;
                  childHeight = this.getPreChildAllHeight(
                    lastChildIndex,
                    preData
                  );
                  //下一个元素的位置
                  tempHegiht = this.getStartPoint2(name2, childHeight);
                } else {
                  let label = this.list[index].childs[index2].label;
                  if (
                    label.lastIndexOf(".png") > 0 ||
                    label.lastIndexOf(".xml") > 0 ||
                    label.lastIndexOf(".jpg") > 0 ||
                    label.lastIndexOf(".pdf") > 0
                  ) {
                    childHeight = this.getPreChildAllHeight(
                      lastChildIndex,
                      preData
                    );
                    //下一个元素的位置
                    tempHegiht = this.getStartPoint2(name2, childHeight);
                  }
                }
              } else {
                childHeight = this.getPreChildAllHeight(index2 - 1, preData);
                //下一个元素的位置
                tempHegiht = this.getStartPoint2(name2, childHeight);
              }
            }

            if (index2 == 0) {
              tempHegiht = 0;
            }

            this.divRefs.push(name2);
            this.two(
              this.$refs[name2],
              this.list[index].childs[index2].childs,
              index2,
              tempHegiht,
              this.$refs[name2][0]
            );
          }
        }
      }
    },
    //设置背景颜色
    setBackGroundColor(label, name, data) {
      if (
        label.lastIndexOf("pdf") > 0 ||
        label.lastIndexOf("xml") > 0 ||
        label.lastIndexOf("png") > 0 ||
        label.lastIndexOf("jpg") > 0 ||
        label.lastIndexOf("jpeg") > 0
      ) {
        this.$refs[name][0].style.backgroundColor = "#5CB95E";
        this.$refs[name][0].style.color = "white";
        return;
      }
      let hasChild = true;
      if (!data || data.length <= 0) {
        if (this.isCollapse == false) {
          this.$refs[name][0].style.display = "none";
        }
        if (this.isCollapse) {
          this.$refs[name][0].style.display = "block";
        }

        hasChild = false;
      }
      if (hasChild) {
        this.$refs[name][0].style.backgroundColor = "#5CB95E";
      } else {
        this.$refs[name][0].style.backgroundColor = "#D9544F";
      }
      this.$refs[name][0].style.color = "white";
    },
    /**
     * 获取上一个子集的高度
     * @param preIndex 上一层的下标
     * @param data 上一个子集
     * **/
    getPreChildAllHeight(preIndex, data) {
      let childsHeight = 0;
      if (!data || data.length <= 0) {
        return 20;
      }
      for (let index = 0; index < data.length; index++) {
        let child = `first0-child` + preIndex + `-child` + index;
        childsHeight +=
          parseInt(
            window.getComputedStyle(this.$refs[child][0]).height.split("px")[0]
          ) + 20;
      }
      return childsHeight + 30;
    },
    //1name 上一级的名称 2上一层的子集合
    //下一个起始位置(当前元素的位置+元素高度+(子高+子margin)*n)+父margin
    getStartPoint2(name, preChildHeight) {
      //window.getComputedStyle(this.$refs[name][0]).height.split("px")[0]==''&&window.getComputedStyle(this.$refs[name][0]).height.split("px")[0]=='auto'&&

      if (window.getComputedStyle(this.$refs[name][0]).display == "none") {
        return (this.nextCoordinate += 20 + preChildHeight);
      }

      //div高度
      let height = parseInt(
        window.getComputedStyle(this.$refs[name][0]).height.split("px")[0]
      );
      this.nextCoordinate += height + preChildHeight;

      return this.nextCoordinate;
    },
    /**
     * 第二层
     * **/
    two(el, childData, index, top) {
      if (this.isCollapse == false) {
        if (!childData || childData.length <= 0) {
          let obj = el[0].innerText;
          // if(obj.lastIndexOf("png")<0&&obj.lastIndexOf("jpg")<0&&obj.lastIndexOf("jpeg")<0&&obj.lastIndexOf("xml")<0)
          if (el[0].style.display == "none") {
            return;
          }
        }
      }
      let tempHeight = 0;
      if (el && el.length > 0) {
        el[0].style.left = 300 + "px";
        // let height = window.getComputedStyle(el[0]).height.split("px")[0];
        // this.tempIndex2.push(parseInt(height));
        if (this.tempIndex2.length <= 0) {
          el[0].style.top = 0 + "px";
        } else {
          this.tempIndex2.forEach((s) => {
            tempHeight += s + 10;
          });
        }
      }
      el[0].style.top = top + tempHeight + "px";

      //渲染第三层
      if (childData && childData.length > 0) {
        this.three(childData, el[0], index);
      }
    },
    three(data, el, parentIndex) {
      let temp = [];
      for (let index = 0; index < data.length; index++) {
        let name = `first0` + `-child` + parentIndex + `-child` + index;
        this.divRefs.push(name);
        if (this.$refs[name]) {
          this.$refs[name][0].style.left = 400 + "px";
          this.$refs[name][0].style.backgroundColor = "#5CB95E";
          let tempHegiht = 0;
          if (temp.length > 0) {
            temp.forEach((s) => {
              tempHegiht += s + 30;
            });
          }
          this.$refs[name][0].style.top = tempHegiht + "px";
          let childHeight = window
            .getComputedStyle(this.$refs[name][0])
            .height.split("px")[0];
          temp.push(parseInt(childHeight));
        }
      }
    },
    //画线
    initLine() {
      //第一层div
      let firstDiv = document.getElementById("firstDiv");
      if (firstDiv.style.display == "block" || firstDiv.style.display == "") {
        for (let index = 0; index < this.list.length; index++) {
          let tempName = "twoDiv-" + index;
          let tempTwo = document.getElementById(tempName);
          if (tempTwo.style.display == "block" || tempTwo.style.display == "") {
            let line = new LeaderLine(firstDiv, tempTwo, this.styleOption);
            this.line.push(line);
            this.initChildLine(tempName, this.list[index].childs);
          }
        }
      }
    },
    initChildLine(tempName, data) {
      if (!data || data.length <= 0) {
        return;
      }
      //第一层div
      let firstDiv = document.getElementById(tempName);
      if (firstDiv.style.display == "block" || firstDiv.style.display == "") {
        for (let index = 0; index < data.length; index++) {
          let tempName = "threeDiv-" + index;
          let tempTwo = document.getElementById(tempName);
          if (tempTwo.style.display == "block" || tempTwo.style.display == "") {
            let line = new LeaderLine(firstDiv, tempTwo, this.styleOption);
            this.line.push(line);
            this.initChildThreeLine(index, tempName, data[index].childs);
          }
        }
      }
    },
    initChildThreeLine(preIndex, tempName2, data) {
      if (!data || data.length <= 0) {
        return;
      }
      //第一层div
      let firstDiv = document.getElementById(tempName2);
      if (firstDiv.style.display == "block" || firstDiv.style.display == "") {
        for (let index = 0; index < data.length; index++) {
          let tempName = "foreDiv-" + preIndex + "-" + index;
          let tempTwo = document.getElementById(tempName);
          if (tempTwo.style.display == "block" || tempTwo.style.display == "") {
            let line = new LeaderLine(firstDiv, tempTwo, this.styleOption);
            this.line.push(line);
          }
        }
      }
    },
    //显示画线
    showLine() {
      let lineArray = document.getElementsByClassName("leader-line");

      for (let index = 0; index < lineArray.length; index++) {
        lineArray[index].style.zIndex = 3000;
      }
      this.line.forEach((a) => {
        a.positionByWindowResize = false;
        a.position();
        a.show();
      });
    },
    //隐藏画线
    hideLine() {
      let lineArray = document.getElementsByClassName("leader-line");

      for (let index = 0; index < lineArray.length; index++) {
        lineArray[index].style.display = "none";
      }
      this.line.forEach((a) => {
        a.positionByWindowResize = false;
        a.position();
        a.hide();
      });
    },
    //注册事件
    registerEvent() {
      let that = this;
      this.$nextTick((a) => {
        window.addEventListener(
          "scroll",
          function () {
            if (that.line && that.line.length > 0) {
              that.line.forEach((a) => {
                a.positionByWindowResize = false;
                a.position();
              });
            }
          },

          true
        );
      });
    },
  },
  destroyed() {
    this.line.forEach((a) => {
      a.remove();
    });
  },
};
</script>
<style scoped>
.zipHome {
  width: 100%;
  position: relative;
}
.zipHome .contentDiv {
  width: 10%;
  position: absolute;
}
.zipHome .contentDiv .fitst {
  top: 0px;
  padding: 5px;
  left: 0px;
  border-radius: 15px;
  text-align: center;
  width: 100%; 
  position: absolute;
}
</style>

<template>
  <div>
    <el-row :gutter="24" v-if="form !=null">
      <el-form ref="form" :model="form" label-width="200px">
        <el-col :span="8">
          <el-form-item label="业务流水号">
            <el-input v-model="form.sblsh" placeholder="业务流水号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理部门名称">
            <el-input v-model="form.handleOrgName" placeholder="办理部门名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理部门统一社会信用代码">
            <el-input v-model="form.handleOrgCode" placeholder="办理部门统一社会信用代码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理部门所在地行政区域代码">
            <el-input v-model="form.handlerEGionCode" placeholder="办理部门所在地行政区域代码"></el-input>
          </el-form-item>
        </el-col>
        <!--        <el-col :span="8">-->
        <!--          <el-form-item label="办理部门名称">-->
        <!--            <el-input v-model="form.orgName" placeholder="办理部门名称"></el-input>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <!--        <el-col :span="8">-->
        <!--          <el-form-item label="办理部门组织机构代码">-->
        <!--            <el-input v-model="form.orgCode" placeholder="办理部门组织机构代码"></el-input>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="8">
          <el-form-item label="业务办理项编码">
            <el-input v-model="form.taskHandleItem" placeholder="业务办理项编码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办结人员姓名">
            <el-input v-model="form.handleusername" placeholder="办结人员姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办结时间">
            <el-input v-model="form.resultdate" placeholder="办结时间"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="办理结果">
            <el-input v-model="form.resultexplain" placeholder="办件结果描述"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="结果证照类型" v-if="form.resultCetrType">
            <el-input v-model="form.resultCetrType" placeholder="结果证照名称" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办件结果（证件）名称" v-if="form.resultcetrname">
            <el-input v-model="form.resultcetrname" placeholder="结果证照名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办件结果（证件）编号" v-if="form.resultcetrno">
            <el-input v-model="form.resultcetrno" placeholder="结果证照编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收费金额">
            <el-input v-model="form.charge" placeholder="收费金额"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="金额单位代码">
            <el-input v-model="form.monetaryunitcode" placeholder="金额单位代码"></el-input>
          </el-form-item>
        </el-col>


        <el-col :span="8">
          <el-form-item label="备注" v-if="form.remark">
            <el-input v-model="form.remark" placeholder="备注"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据提供部门行政区划代码">
            <el-input v-model="form.prodataregioncode" placeholder="数据提供部门行政区划代码"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="数据提供部门名称">
            <el-input v-model="form.prodataorgname" placeholder="数据提供部门名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务发生事项部门名称" v-if="form.ywfsSxbmmc">
            <el-input v-model="form.ywfsSxbmmc" placeholder="业务发生事项部门名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否为镇街事项">
            <el-input v-model="form.hasTown" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="镇街名称" v-if="form.townName">
            <el-input v-model="form.townName" placeholder="镇街名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据提供部门统一社会信用代码">
            <el-input v-model="form.prodataorgcode" placeholder="数据提供部门统一社会信用代码"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
    <el-row :gutter="24" v-else>
      <div style="text-align: center">暂无数据</div>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => {
      },
    },
  },
  computed: {
    hasTownData() {
    }
  },
  mounted() {
  },

  methods: {},
};
</script>

<style lang="scss" scoped>

</style>

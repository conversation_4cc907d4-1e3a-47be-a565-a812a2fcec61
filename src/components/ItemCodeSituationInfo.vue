<template>
  <div class="box">
    <el-button @click="syncSituationVersions" type="primary" style="margin-bottom: 15px;">同步情形信息</el-button>
    <el-table :data="tableData" style="width: 100%" :height="tableHeight"
              :header-cell-style="{ background: '#F6F7FA' }">
      <el-table-column label="序号" align="center" width="60">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
          prop="siName"
          label="名称"
          align="center"
      ></el-table-column>
      <!--      <el-table-column
              prop="version"
              label="版本"
              align="center"
            ></el-table-column>-->
      <el-table-column
        prop="unSid"
        label="情形号"
        align="center" 
      ></el-table-column>  
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="showSituationVersions(scope.$index, scope.row)"
            >版本</el-button
          >
        </template>
      </el-table-column>
    </el-table>
<!--查看本地版本信息-->
    <el-dialog
        :title="allSitutationVersionDialog.title"
        fullscreen
        :visible.sync="allSitutationVersionDialog.dialogVisible"
        :append-to-body="true"
        :close-on-click-modal="true"
    >
      <el-table :data="allSitutationVersionData" style="width: 100%" height="550" :header-cell-style="{ background: '#F6F7FA' }">
        <el-table-column label="序号" align="center" width="60">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
            prop="siName"
            label="情形名称"
            align="center"
        ></el-table-column>
        <el-table-column
            prop="version"
            label="情形版本"
            align="center"
        ></el-table-column>
        <el-table-column
            prop="unSid"
            label="情形号"
            align="center"
        ></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
                type="text"
                icon="el-icon-files"
                @click="handleMaterial(scope.$index, scope.row)"
            >材料</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="save('ruleForm')">确 定</el-button> -->
        <el-button @click="allSitutationVersionDialog.dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
<!--查看本地版本材料-->
    <el-dialog 
      :title="materialCatalogueDialog.title"
      fullscreen
      :visible.sync="materialCatalogueDialog.dialogVisible"
      :before-close="handleClose"
      :append-to-body="true"
      :close-on-click-modal="true"
    >
      <MaterialCatalogue
        :itemCode="ruleForm.itemId"
        :unSid="ruleForm.unSid"
        :versionId="ruleForm.versionId"
        v-if="materialCatalogueDialog.dialogVisible"
      ></MaterialCatalogue>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="save('ruleForm')">确 定</el-button> -->
        <el-button @click="cancel('ruleForm')">关 闭</el-button>
      </span>
    </el-dialog>
<!--同步情形信息dialog  -->
    <el-dialog
        :title="situationInfoDialog.title"
        fullscreen
        :visible.sync="situationInfoDialog.dialogVisible"
        :append-to-body="true"
        :close-on-click-modal="true"
    >
      <el-table :data="situationInfoData" style="width: 100%" height="calc(100% - 10rem)"
                :header-cell-style="{ background: '#F6F7FA' }">
        <el-table-column label="序号" align="center" width="60">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
            prop="siName"
            label="情形名称"
            align="center"
        ></el-table-column>
        <el-table-column
            prop="siStatus"
            label="情形状态"
            align="center"
        >
          <template slot-scope="scope">
            <!-- scope.row 包含表格里当前行的所有数据 -->
            <span>{{scope.row.siStatus === `1` ? `启用`:`不启用`}}</span>
          </template>
        </el-table-column>
        <el-table-column
            prop="linkPhone"
            label="咨询电话"
            align="center"
        >
          <template slot-scope="scope">
            <!-- scope.row 包含表格里当前行的所有数据 -->
            <span>{{scope.row.linkPhone !== null ? scope.row.linkPhone:`无`}}</span>
          </template>
        </el-table-column>
        <el-table-column
            prop="unSid"
            label="情形项唯一标识码"
            align="center"
        ></el-table-column>
        <el-table-column
            prop="versionLists.length"
            label="总版本数"
            align="center"
        ></el-table-column>


        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
                type="text"
                icon="el-icon-edit"
                @click="showVersions(scope.$index, scope.row)"
            >版本</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="save('ruleForm')">确 定</el-button> -->
        <el-button @click="cancel('ruleForm')">关 闭</el-button>
      </span>
    </el-dialog>
    <!--同步情形版本dialog-->
    <el-dialog
        :title="situationVersionsDialog.title"
        fullscreen
        :visible.sync="situationVersionsDialog.dialogVisible"
        :before-close="handleSituationVersionsDialogClose"
        :append-to-body="true"
        center
        :close-on-click-modal="true"
    >
      <el-table
          ref="situationVersionsTable"
                :data="situationVersionsData"
                style="width: 100%"
                tooltip-effect="dark"
                height="550" :header-cell-style="{ background: '#F6F7FA' }"
                @selection-change="handleSelectionChange">
        <el-table-column
            type="selection"
            width="55">
        </el-table-column>
        <el-table-column label="序号" align="center" width="60">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
            prop="sitemId"
            label="所属情形项目id"
            align="center"
        ></el-table-column>
        <el-table-column
            prop="siVersion"
            label="版本号"
            align="center"
        ></el-table-column>
        <el-table-column
            prop="siStatus"
            label="版本状态"
            align="center"
        >
          <template slot-scope="scope">
            <!-- scope.row 包含表格里当前行的所有数据 -->
            <span>{{scope.row.siStatus === `1` ? `当前版本`:`历史版本`}}</span>
          </template>
        </el-table-column>
        <el-table-column
            prop="updateTime"
            label="修改时间"
            align="center"
        >
          <template slot-scope="scope">
            <!-- scope.row 包含表格里当前行的所有数据 -->
            <span>{{parseTime(Number(scope.row.updateTime))}}</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
         <el-button type="primary" @click="handleSyncVersions" :disabled="buttonStatus">同 步</el-button>
        <el-button @click="handleSituationVersionsDialogClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { itemCodeApi } from "../api/itemCode";
import MaterialCatalogue from "./MaterialCatalogue.vue";
import {tool} from "../utils/tool";
export default {
  props: {
    itemCode: String,
    unSid: String,
  },
  components: {
    MaterialCatalogue,
  },
  created(){
      this.getData()
  },
  data() {
    return {
      buttonStatus: true,
      tableData:[],
      materialCatalogueDialog: {
        title: "材料信息",
        dialogVisible: false,
      },
      situationInfoDialog: {
        title: "同步情形信息",
        dialogVisible: false,
      },
      situationVersionsDialog: {
        title: "同步情形版本信息",
        dialogVisible: false,
      },
      allSitutationVersionDialog: {
        title: "本地情形版本信息",
        dialogVisible: false,
      },
      situationInfoData: [],
      situationVersionsData: [],
      situationInfoRow: {},
      allSitutationVersionData: [],
    };
  },
  computed: {
    tableHeight: function () {
      return (window.innerHeight - 300) + 'px';
    }
  },
  methods: {
    // 表格是否选中数据行的判断
    handleSelectionChange(val) {
      console.log(val.length, 123)
      if (val.length > 0) {
        this.buttonStatus = false
      } else {
        this.buttonStatus = true
      }
    },
    //查询事项的情形
      getData(){
          itemCodeApi.getSituationInfos({itemCode:this.itemCode}).then(a=>{
            this.tableData=a.data;
          })
      },
    //同步情形版本
    syncSituationVersions(){
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "正在同步数据中...", //显示在加载图标下方的加载文案
        target: document.querySelector("#el-table"), //loadin覆盖的dom元素节点
      });
      itemCodeApi.syncSituationVersions({itemCode:this.itemCode}).then(res =>{
        if (res.code === 200){
          this.situationInfoDialog.dialogVisible = true
          this.situationInfoData = res.data;
        }
        loading.close();
      })
    },
    //显示版本信息
    showVersions(index,row){
      this.situationInfoRow = row
      this.situationVersionsData = row.versionLists;
      this.situationVersionsDialog.dialogVisible = true
    },
    //查询情形版本
    showSituationVersions(index,row){
      this.allSitutationVersionDialog.dialogVisible = true;
      this.getAllSitutationVersion(row)
    },
      handleClose(done){
        this.materialCatalogueDialog.dialogVisible = false;
        this.situationVersionsData.dialogVisible = false;
      },
      cancel(){
        this.materialCatalogueDialog.dialogVisible = false;
        this.situationInfoDialog.dialogVisible = false;
        this.ruleForm = {};
      },
      handleSituationVersionsDialogClose(){
          this.situationVersionsDialog.dialogVisible = false;
      },
      handleMaterial(index, row) {
        this.ruleForm = row;
        this.materialCatalogueDialog.dialogVisible = true;
      },
      handleSyncVersions(){
        const loading = this.$loading({
          lock: true, //lock的修改符--默认是false
          text: "Loading", //显示在加载图标下方的加载文案
          target: document.querySelector("#Table"), //loadin覆盖的dom元素节点
        });
        let versionLists = this.$refs.situationVersionsTable.selection;
        const situationInfoRow = {};
        Object.assign(situationInfoRow,this.situationInfoRow);
        situationInfoRow.versionLists = versionLists;
        itemCodeApi.saveSituationVersions({itemCode:this.itemCode,situationInfo:situationInfoRow}).then(res=>{
          if (res.code === 200){
            this.$message.success("同步成功")
          }else {
            this.$message.error("同步失败")
          }
          loading.close()
          this.situationVersionsDialog.dialogVisible = false
        })
      },
      getAllSitutationVersion(row){
        itemCodeApi.getAllSituationVersions({itemCode:this.itemCode,unSid:row.unSid}).then(res =>{
          this.allSitutationVersionData = res.data
        })
      },
    parseTime(timestamp){
      let date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let Y = date.getFullYear() + '-';
      let M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
      let D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
      let h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
      let m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
      let s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());

      let strDate = Y+M+D+h+m+s;
      return strDate;
    }
  },
};
</script>
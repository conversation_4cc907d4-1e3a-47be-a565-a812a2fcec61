<template>
  <div>
    <el-row :gutter="20">
      <el-input v-model="form.id" v-show="false"></el-input>
      <!--     非建设工程表单 -->
      <el-form ref="form" :model="form" label-width="200px" v-if="form.applyinstId == null">
        <el-col :span="8">
          <el-form-item label="业务流水号" prop="sblsh">
            <el-input v-model="form.sblsh" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项编码" prop="itemCode">
            <el-input v-model="form.itemCode" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="电子文件号">
            <el-input v-model="form.documentNumber" placeholder="电子文件号" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申报事项名称" prop="sbxmmc">
            <el-input v-model="form.sbxmmc" placeholder="申报事项名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理部门名称" prop="applyDeptName">
            <el-input v-model="form.applyDeptName" placeholder="受理部门名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任处（科）室" prop="applyDeptName">
            <el-input v-model="form.applyDeptName" placeholder="责任处（科）室"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批区域代码">
            <el-input v-model="form.approveAreaCode" placeholder="审批区域代码"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="案卷号" prop="archivesNo">
            <el-input v-model="form.archivesNo" readonly placeholder="案卷号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行政相对人名称">
            <el-input v-model="form.bsName" placeholder="办事人名/企业名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行政相对人手机">
            <el-input v-model="form.bsPhone" placeholder="行政相对人手机"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行政相对人电话">
            <el-input v-model="form.bsPhone" placeholder="行政相对人电话"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="办事人/企业证件号码">
            <el-input v-model="form.bsNum" placeholder="办事人/企业证件号码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办事人证件类型">
            <el-input v-model="form.bsCardType" readonly placeholder="办事人证件类型"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否代办">
            <el-input v-model="form.isAgent" readonly></el-input>
          </el-form-item>
        </el-col>
        <div>
          <el-col :span="8" v-if="form.agentName">
            <el-form-item label="代办人名/企业名">
              <el-input v-model="form.agentName" placeholder="代办人名/企业名"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="form.agentPhone">
            <el-form-item label="代办人手机号码">
              <el-input v-model="form.agentPhone" placeholder="代办人手机号码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.agentNum">
            <el-form-item label="代办人/企业证件号码">
              <el-input v-model="form.agentNum" placeholder="代办人/企业证件号码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.agentCardType">
            <el-form-item label="代办人证件类型">
              <el-input v-model="form.agentCardType" placeholder="代办人证件类型"></el-input>
            </el-form-item>
          </el-col>
        </div>
        <el-col :span="8">
          <el-form-item label="经办人名称" v-if="form.jname">
            <el-input v-model="form.jname" placeholder="经办人名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经办人手机号码" v-if="form.jphone">
            <el-input v-model="form.jphone" placeholder="经办人手机号码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经办人证件号" v-if="form.jnum">
            <el-input v-model="form.jnum" placeholder="经办人证件号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经办人邮箱" v-if="form.jmail">
            <el-input v-model="form.jmail" placeholder="经办人邮箱"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经办人证件类型" v-if="form.jCardType">
            <el-input v-model="form.jCardType" placeholder="经办人证件类型"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理形式">
            <el-input v-model="form.applyType" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建时间">
            <el-input v-model="form.createDate" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理（立案）时间">
            <el-input v-model="form.applyDate" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理（承办）单位">
            <el-input v-model="form.applyDeptName" placeholder="受理（承办）单位"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收件省份" v-if="form.sendProvince">
            <el-input v-model="form.sendProvince" placeholder="收件省份"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收件城市" v-if="form.sendCity">
            <el-input v-model="form.sendCity" placeholder="收件城市"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收件区域" v-if="form.sendTown">
            <el-input v-model="form.sendTown" placeholder="收件区域"></el-input>
          </el-form-item>
        </el-col>
        <div>

        </div>
        <div v-if="this.form.Custom">
          <el-col :span="24">
            <p class="text">自定义数据项</p>
          </el-col>
          <div v-for="(item, index) in form.Custom" :key="index">
            <el-col :span="8">
              <el-form-item :label="item.note">
                <el-input v-model="item.settingValues"></el-input>
              </el-form-item>
            </el-col>
          </div>
        </div>
      </el-form>
      <!--      建设工程表单-->
      <el-form ref="form" :model="form" label-width="200px" v-if="form.applyinstId != null">
        <el-col :span="8">
          <el-form-item label="业务流水号" prop="sblsh">
            <el-input v-model="form.sblsh" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目名称" prop="projName">
            <el-input v-model="form.projName" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目代码" prop="localCode">
            <el-input v-model="form.localCode" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目国家统一代码">
            <el-input v-model="form.centralCode" placeholder="项目国家统一代码" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="案卷号" prop="archivesNo">
            <el-input v-model="form.archivesNo" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="电子文件号">
            <el-input v-model="form.documentNumber" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申报来源" prop="applyinstSource">
            <el-input v-model="form.applyinstSource" placeholder="申报来源"
                      readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申报时间" prop="sbTime">
            <el-input v-model="form.sbTime" placeholder="申报时间" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项编码" prop="iteminstCode">
            <el-input v-model="form.iteminstCode" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项名称" prop="iteminstName">
            <el-input v-model="form.iteminstName" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批部门名称">
            <el-input v-model="form.orgName" placeholder="审批部门名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人名字">
            <el-input v-model="form.addresseeName" placeholder="收件人名字"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人号码">
            <el-input v-model="form.addresseePhone" placeholder="收件人号码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人身份证">
            <el-input v-model="form.addresseeIdcard" placeholder="收件人身份证"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人所在地址">
            <el-input v-model="form.addresseeAddr" placeholder="收件人所在地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人代表姓名">
            <el-input v-model="form.representative" placeholder="法人代表姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人代表电话">
            <el-input v-model="form.representativeTel" placeholder="法人代表电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人代表身份证">
            <el-input v-model="form.representativeIdNo" placeholder="法人代表身份证"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人名称">
            <el-input v-model="form.contact" placeholder="联系人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人电话">
            <el-input v-model="form.contactMobile" placeholder="联系人电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人身份证">
            <el-input v-model="form.contactIdNo" placeholder="联系人身份证"></el-input>
          </el-form-item>
        </el-col>


        <div>

        </div>
        <div v-if="this.form.Custom">
          <el-col :span="24">
            <p class="text">自定义数据项</p>
          </el-col>
          <div v-for="(item, index) in form.Custom" :key="index">
            <el-col :span="8">
              <el-form-item :label="item.note">
                <el-input v-model="item.settingValues"></el-input>
              </el-form-item>
            </el-col>
          </div>
        </div>
      </el-form>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => {

      },
    },
  },
  data() {
    return {
      rules:{
        sblsh :[{required: true, message: '请输入', trigger: 'blur'}],
        archivesNo :[{required: true, message: '请输入', trigger: 'blur'}],
        sbxmmc :[{required: true, message: '请输入', trigger: 'blur'}],
      }
    };
  },
  created() {
  },

  mounted() {
  },

  methods: {
    save() { },
    cancel() { },
  },
};
</script>
<style scoped>
.text {
  font-size: 18px;
  margin-left: 60px;
}

</style>

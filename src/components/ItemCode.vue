<template>
  <div class="box">
    <div class="searchDiv1">
      <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
        <el-form-item label="事项编码">
          <el-input v-model="query.itemCode" placeholder="事项编码"></el-input>
        </el-form-item>
        <el-form-item label="事项名称">
          <el-input v-model="query.name" placeholder="事项名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%" height="600" :header-cell-style="{ background: '#F6F7FA' }">
      <el-table-column label="序号" align="center" width="60">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="taskCode" label="事项编码" align="center"></el-table-column>
      <el-table-column prop="taskName" label="事项名称" align="center"></el-table-column>

      <el-table-column prop="taskTypeText" label="事项类型" align="center"></el-table-column>
      <el-table-column prop="createDate" label="期限" width="100" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.retentionPeriod != null">{{
              scope.row.retentionPeriod.name
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createDate" label="密级" width="100" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.classificationType != null">{{
              scope.row.classificationType.name
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="itemPattern" label="对接模式" width="100" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.itemPattern == 1">模式一</span>
          <span v-if="scope.row.itemPattern == 2">模式二</span>
        </template>
      </el-table-column>
      <el-table-column label="同步状态" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.isNeed === 0 ? "同步中" : "未开启同步" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleEdit(scope.$index, scope.row)">编辑
          </el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleRemove(scope.$index, scope.row)">删除
          </el-button>
          <el-button type="text" icon="el-icon-s-operation" @click="handleSituation(scope.$index, scope.row)">情形
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:page-size="query.pageSize" :page-sizes="[10, 20, 50, 100]"
                   layout="total, sizes, prev, pager, next, jumper" :total="query.total" @size-change="handleSizeChange"
                   @current-change="handleCurrentChange" style="float: right; margin-top: 10px;"/>
    <!-- <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="query.pageNoTemp"
        :page-size="query.pageSize"
        layout="total, prev, pager, next"
        :total="query.total"
    >
    </el-pagination> -->
    <!-- 事项编辑-->
    <el-dialog :close-on-press-escape="false" :title="dialog.title" :visible.sync="dialog.dialogVisible"
               :before-close="cancel" :append-to-body="true" center :close-on-click-modal="true" width="75%">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="部门事项" name="first">
          <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" size="small" label-width="auto"
                   class="demo-ruleForm">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="事项编码" prop="taskCode">
                  <el-input v-model="ruleForm.taskCode" autocomplete="off">
                    <template slot="append">
                      <el-button class="syncItemBtn" size="medium" type="primary" @click="syncItemCode">同步事项
                      </el-button>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事项名称" prop="taskName">
                  <el-input v-model="ruleForm.taskName" autocomplete="off" disabled></el-input>
                </el-form-item>
              </el-col>

              <!-- <el-col :span="8">
                <el-form-item label="案卷号规则配置" prop="ArchivesNumber">
                  <el-select
                    v-model="ruleForm.ArchivesNumber"
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in ArchivesNumber"
                      :key="item.id"
                      :label="item.ruleNme"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->
              <!-- <el-col :span="8">
                <el-form-item label="档号规则配置" prop="fileNumber">
                  <el-select
                    v-model="ruleForm.fileNumber"
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in fileNumber"
                      :key="item.id"
                      :label="item.ruleNme"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->

              <el-col :span="12">
                <el-form-item label="期限" prop="retentionPeriodId">
                  <el-select v-model="ruleForm.retentionPeriodId" filterable placeholder="请选择" style="width: 100%">
                    <el-option v-for="item in retentionperiod" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="密级" prop="classificationTypeId">
                  <el-select v-model="ruleForm.classificationTypeId" filterable placeholder="请选择"
                             style="width: 100%">
                    <el-option v-for="item in cclassType" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="同步" prop="isNeed">
                  <el-switch v-model="ruleForm.isNeed" active-color="#13ce66" inactive-color="#C0CCDA" :active-value="0"
                             :inactive-value="1"></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="过程容缺" prop="gcsfrq">
                  <el-switch v-model="ruleForm.gcsfrq" active-color="#13ce66" inactive-color="#C0CCDA" :active-value="1"
                             :inactive-value="0"></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="办结容缺" prop="bjsfrq">
                  <el-switch v-model="ruleForm.bjsfrq" active-color="#13ce66" inactive-color="#C0CCDA" :active-value="1"
                             :inactive-value="0"></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="关联镇街" prop="isSyncZj">
                  <el-switch v-model="ruleForm.isSyncZj" active-color="#13ce66" inactive-color="#C0CCDA"
                             :active-value="1" :inactive-value="0"></el-switch>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="四性检测">
                  <el-button @click="handOpenFourCheckDialog">
                    四性检测配置
                  </el-button>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="元数据">
                  <el-button @click.native="handleMetadata">元数据配置</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办件类型" prop="projectTypeText">
                  <el-input disabled v-model="ruleForm.projectTypeText" autocomplete="off"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="部门类型" prop="deptTypeText">
                  <el-input disabled v-model="ruleForm.deptTypeText" autocomplete="off"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="执行部门" prop="deptName">
                  <el-input disabled v-model="ruleForm.deptName" autocomplete="off"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="部门编码" prop="deptCode">
                  <el-input disabled v-model="ruleForm.tongYiCode" autocomplete="off"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="服务类型" prop="serveTypeText">
                  <el-input disabled v-model="ruleForm.serveTypeText" autocomplete="off"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="事项类型" prop="taskTypeText">
                  <el-input disabled v-model="ruleForm.taskTypeText" autocomplete="off"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事项版本" prop="taskVersion">
                  <el-input disabled v-model="ruleForm.taskVersion" autocomplete="off"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="类别号" prop="categoryNumber">
                  <el-input placeholder="请输入类别号" v-model="ruleForm.categoryNumber"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="对接模式" prop="itemPattern">
                  <el-radio v-model="ruleForm.itemPattern" label="1">模式一</el-radio>
                  <el-radio v-model="ruleForm.itemPattern" label="2">模式二</el-radio>
                </el-form-item>
              </el-col>

              <!--          <el-col :span="8">
                          <el-form-item label="同步间隔" prop="syncTime">
                            <el-select v-model="ruleForm.syncTime" placeholder="请选择" style="width: 100%">
                              <el-option v-for="item in syncTimeData" :key="item.dvalue" :label="item.dname" :value="item.dvalue">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>-->
              <!-- <el-input  v-model="ruleForm.syncTime" autocomplete="off" placeholder="请选择同步时间"></el-input> -->
            </el-row>

            <el-row>
              <el-col :span="8">
                <el-form-item label="同步开始时间" prop="startTime">
                  <el-date-picker v-model="ruleForm.startTime" type="date" align="right" unlink-panels
                                  placeholder="开始日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                  style="width: 100%;">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 市级部门配置 -->
            <el-row>
              <el-col :span="4">
                <el-form-item label="指定市级办内科部门" prop="isMunicipalDept">
                  <el-switch v-model="ruleForm.isMunicipalDept" active-color="#13ce66" inactive-color="#C0CCDA"
                             :active-value="true" :inactive-value="false"
                             @change="handleMunicipalDeptChange"></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="20" v-if="ruleForm.isMunicipalDept">
                <div style="margin-left: 20px; display: inline-block;">
                  <el-form-item label="市级部门名称" prop="municipalDeptName"
                                style="display: inline-block; margin-right: 20px;">
                    <el-input v-model="ruleForm.municipalDeptName" placeholder="请输入市级部门名称"
                              style="width: 220px;" size="small"></el-input>
                  </el-form-item>
                  <el-form-item label="市级部门编号" prop="municipalDeptCode"
                                style="display: inline-block; margin-right: 20px;">
                    <el-input v-model="ruleForm.municipalDeptCode" placeholder="请输入市级部门编号"
                              style="width: 220px;" size="small"></el-input>
                  </el-form-item>
                  <el-form-item label="市级全宗号" prop="municipalQzh" style="display: inline-block;">
                    <el-input v-model="ruleForm.municipalQzh" placeholder="请输入市级全宗号"
                              style="width: 220px;" size="small"></el-input>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>

            <!-- 镇街部门配置 -->
            <el-row>
              <el-col :span="4">
                <el-form-item label="指定镇街办内科部门" prop="isTownDept">
                  <el-switch v-model="ruleForm.isTownDept" active-color="#13ce66" inactive-color="#C0CCDA"
                             :active-value="true" :inactive-value="false" @change="handleTownDeptChange"></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="20" v-if="ruleForm.isTownDept">
                <div style="margin-left: 20px; display: inline-block;">
                  <el-form-item label="镇街部门名称" prop="townDeptName"
                                style="display: inline-block; margin-right: 20px;">
                    <el-input v-model="ruleForm.townDeptName" placeholder="请输入镇街部门名称"
                              style="width: 220px;" size="small"></el-input>
                  </el-form-item>
                  <el-form-item label="镇街部门编号" prop="townDeptCode"
                                style="display: inline-block; margin-right: 20px;">
                    <el-input v-model="ruleForm.townDeptCode" placeholder="请输入镇街部门编号"
                              style="width: 220px;" size="small"></el-input>
                  </el-form-item>
                  <el-form-item label="镇街全宗号" prop="townQzh" style="display: inline-block;">
                    <el-input v-model="ruleForm.townQzh" placeholder="请输入镇街全宗号"
                              style="width: 220px;" size="small"></el-input>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>


          </el-form>
          <div align="center">
            <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
            <el-button @click="cancel('ruleForm')">取 消</el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="ruleForm.isSyncZj == 1" label="镇街事项" name="second">
          <!--镇街数据-->
          <el-table :data="zjItemCodeData" style="width: 100%" :header-cell-style="{ background: '#F6F7FA' }"
                    height="500">
            <el-table-column width="50px" prop="date" align="center" label="序号">
              <template slot-scope="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="taskName" label="事项名称"></el-table-column>
            <el-table-column align="center" prop="taskCode" label="事项编码"></el-table-column>
            <el-table-column align="center" prop="deptName" label="部门名称"></el-table-column>
            <el-table-column align="center" prop="tongYiCode" label="部门编码"></el-table-column>
            <el-table-column align="center" prop="taskTypeText" label="事项类型"></el-table-column>
            <el-table-column align="center" prop="projectTypeText" label="办件类型"></el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-edit" @click="handleEditZjItemCode(scope.$index, scope.row)">编辑
                </el-button>
                <el-button type="text" icon="el-icon-delete" @click="deleteZjItemCode(scope.$index, scope.row)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

    </el-dialog>
    <!-- 镇街事项编辑-->
    <el-dialog :close-on-press-escape="false" :title="editZjItemCode.title" :visible.sync="editZjItemCode.dialogVisible"
               :append-to-body="true" center :close-on-click-modal="true" width="60%">
      <el-form :model="ruleForm2" status-icon :rules="rules" ref="ruleForm2" size="small" label-width="auto"
               class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事项编码" prop="taskCode">
              <el-input v-model="ruleForm2.taskCode" autocomplete="off" disabled>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事项名称" prop="taskName">
              <el-input v-model="ruleForm2.taskName" autocomplete="off" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="办件类型" prop="projectTypeText">
              <el-input disabled v-model="ruleForm2.projectTypeText" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门类型" prop="deptTypeText">
              <el-input disabled v-model="ruleForm2.deptTypeText" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行部门" prop="deptName">
              <el-input disabled v-model="ruleForm2.deptName" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门编码" prop="deptCode">
              <el-input disabled v-model="ruleForm2.tongYiCode" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务类型" prop="serveTypeText">
              <el-input disabled v-model="ruleForm2.serveTypeText" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事项类型" prop="taskTypeText">
              <el-input disabled v-model="ruleForm2.taskTypeText" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事项版本" prop="taskVersion">
              <el-input disabled v-model="ruleForm2.taskVersion" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="类别号" prop="categoryNumber">
              <el-input placeholder="请输入类别号" v-model="ruleForm2.categoryNumber"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="saveZjItemCode('ruleForm2')">确 定</el-button>
        <el-button @click="editZjItemCode.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <!--  情形-dialog  -->
    <el-dialog :close-on-press-escape="false" :title="materialCatalogueDialog.title" fullscreen
               :visible.sync="materialCatalogueDialog.dialogVisible" :before-close="handleClose" :append-to-body="true"
               :close-on-click-modal="true">
      <ItemCodeSituationInfo :itemCode="ruleForm.taskCode" v-if="materialCatalogueDialog.dialogVisible">
      </ItemCodeSituationInfo>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="save('ruleForm')">确 定</el-button> -->
        <el-button @click="cancel('ruleForm')">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="部门事项同步结果" width="70%" :close-on-press-escape="false" fullscreen
               :visible.sync="syncDialog.dialogVisible" :append-to-body="true" center :close-on-click-modal="true"
               :before-close="cancelSyncItemCodeDialog">
      <el-divider content-position="left">部门事项</el-divider>
      <el-table :data="syncTableData" style="width: 100%" border>
        <el-table-column width="50px" prop="date" align="center" label="序号">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="taskName" label="事项名称"></el-table-column>
        <el-table-column align="center" prop="taskCode" label="事项编码"></el-table-column>
        <el-table-column align="center" prop="deptName" label="部门名称"></el-table-column>
        <el-table-column width="100px" align="center" prop="taskVersion" label="版本号"></el-table-column>
        <el-table-column align="center" prop="taskTypeText" label="事项类型"></el-table-column>
        <el-table-column width="100px" align="center" prop="date" label="操作">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit" @click="dataSync(scope.$index, scope.row)">同步
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog :close-on-press-escape="false" :title="selectMetadata.title" :visible.sync="selectMetadata.dialogVisible"
               :before-close="handleClose2" :append-to-body="true" center :close-on-click-modal="true">
      <SelectXML :taskCode="this.ruleForm.taskCode" @transfer="getIds"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveIds">新增</el-button>
        <el-button @click="selectMetadata.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <!--元数据管理-->
    <el-dialog width="30%" center :close-on-press-escape="false" title="新增" :visible.sync="addDialogVisible"
               :before-close="handleClose" :append-to-body="true" :close-on-click-modal="true">
      <el-form :model="addForm" :rules="rules2" ref="ruleForms" label-width="110px" class="demo-ruleForm">
        <el-form-item label="元数据标签名">
          <el-input v-model="addForm.setting_name"></el-input>
        </el-form-item>
        <el-form-item label="元数据标题">
          <el-input v-model="addForm.note"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSaveIds('ruleForms')">确定</el-button>
        <el-button @click="removeForm('ruleForms')">取 消</el-button>
      </span>
    </el-dialog>
    <!--四性检测-->
    <el-dialog
        width="76%"
        top="100px"
        title="四性检测配置"
        custom-class="configureDialog"
        :append-to-body="true"
        :visible="fourCheckdialog.dialogVisible"
        :before-close="handleCloseFourCheck"
        center
    >
      <div>
        <el-form
            class="configureForm">
          <el-table
              ref="resultTable"
              class="resultTable"
              :data="fourTestTableData"
              :row-style="{ height: '0' }"
              :cell-style="{ padding: '0' }"
              width="100%"
              height="100%"
          >
            <el-table-column label="编号" align="center" prop="id">
            </el-table-column>
            <el-table-column label="检测分类" align="center" prop="four_check">
            </el-table-column>
            <el-table-column label="检测项名称" align="center" prop="check_options">
            </el-table-column>
            <el-table-column label="检测内容" align="center" prop="content" show-overflow-tooltip>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
<!--        <el-button type="primary" @click="submit">确 定</el-button>-->
        <el-button @click="handleCloseFourCheck">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {itemCodeApi} from "../api/itemCode";
import ItemCodeSituationInfo from "./ItemCodeSituationInfo.vue";
import {retentionperiodApi} from "../api/retentionperiod";
import {dataSetApi} from "../api/dataSet";
import {classTypeApi} from "../api/classType";
import SelectXML from "./SelectXML.vue";
import {addMatedate, FourChecks, getMatedateAll, selectItemCodeFourChecked} from "../api/first";
import {itemBusinessApi} from "@/api/itemBusiness";

export default {
  components: {
    ItemCodeSituationInfo,
    SelectXML,
  },
  props: {
    bmbm: String,
    bmName: String,
  },
  watch: {},
  data() {
    let syncTimeCheck = (rule, value, callback) => {
      if (this.ruleForm.isNeed === 1 && !value) {
        return callback(new Error("同步时间不能为空"));
      }
      if (this.ruleForm.startTime == null) {
        return callback(new Error("同步开始时间不能为空"));
      }
      return callback();
    };
    return {
      fourTestId: [],
      fourTestTableData: [],
      activeName: 'first',
      treeData: [],
      zjItemCodeData: [],
      checkedNodes: [],
      defaultList: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      ids: [],
      ItemCodeAttribute: [],
      syncTimeData: [],
      syncTableData: [],
      syncTableData2: [],
      retentionperiod: [],
      cclassType: [],
      addDialogVisible: false,
      value1: "",
      startTimeAndEndTime: [],
      query: {
        bmbm: this.bmbm,
        pageNo: 0,
        pageSize: 10,
        total: 0,
      },
      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      syncDialog: {
        dialogVisible: false,
      },
      fourCheckdialog: {
        dialogVisible: false,
      },
      materialCatalogueDialog: {
        title: "情形信息",
        dialogVisible: false,
      },
      selectMetadata: {
        title: "选择元数据",
        dialogVisible: false,
      },
      editZjItemCode: {
        title: "编辑镇街事项",
        dialogVisible: false,
      },
      rules2: {
        setting_name: [
          {required: true, message: "请输入元数据标签名", trigger: "blur"},
        ],
        note: [
          {required: true, message: "请输入元数据标题", trigger: "blur"},
        ],
      },
      rules: {
        startTime: [{required: true, validator: syncTimeCheck, trigger: "blur"}],
        syncTime: [{required: true, message: "同步间隔不能为空", trigger: "blur"}],
        taskCode: [
          {required: true, message: "事项编码不能为空", trigger: "blur"},
        ],
        taskName: [
          {required: true, message: "事项名称不能为空", trigger: "blur"},
        ],
        retentionPeriodId: [
          {required: true, message: "期限不能为空", trigger: "blur"},
        ],
        classificationTypeId: [
          {required: true, message: "密级不能为空", trigger: "blur"},
        ],
        projectTypeText: [
          {required: true, message: "办件类型不能为空", trigger: "blur"},
        ],
        deptTypeText: [
          {required: true, message: "部门类型不能为空", trigger: "blur"},
        ],
        deptName: [
          {required: true, message: "执行部门不能为空", trigger: "blur"},
        ],
        deptCode: [
          {required: true, message: "部门编码不能为空", trigger: "blur"},
        ],
        serveTypeText: [
          {required: true, message: "服务类型不能为空", trigger: "blur"},
        ],
        taskTypeText: [
          {required: true, message: "事项类型不能为空", trigger: "blur"},
        ],
        taskVersion: [
          {required: true, message: "事项版本不能为空", trigger: "blur"},
        ],
        categoryNumber: [
          {required: true, message: "类别号不能为空", trigger: "blur"}
        ],
        itemPattern: [
          {required: true, message: "模式不能为空", trigger: "blur"}
        ],
        // 市级部门相关验证规则
        municipalDeptName: [
          {
            validator: (rule, value, callback) => {
              if (this.ruleForm.isMunicipalDept && !value) {
                callback(new Error("市级部门名称不能为空"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        municipalDeptCode: [
          {
            validator: (rule, value, callback) => {
              if (this.ruleForm.isMunicipalDept && !value) {
                callback(new Error("市级部门编号不能为空"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        municipalQzh: [
          {
            validator: (rule, value, callback) => {
              if (this.ruleForm.isMunicipalDept && !value) {
                callback(new Error("市级全宗号不能为空"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        // 镇街部门相关验证规则
        townDeptName: [
          {
            validator: (rule, value, callback) => {
              if (this.ruleForm.isTownDept && !value) {
                callback(new Error("镇街部门名称不能为空"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        townDeptCode: [
          {
            validator: (rule, value, callback) => {
              if (this.ruleForm.isTownDept && !value) {
                callback(new Error("镇街部门编号不能为空"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        townQzh: [
          {
            validator: (rule, value, callback) => {
              if (this.ruleForm.isTownDept && !value) {
                callback(new Error("镇街全宗号不能为空"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ]

      },
      ruleForm: {
        checkId: "",
        // 市级部门和镇街部门相关字段
        isMunicipalDept: false,
        isTownDept: false,
        municipalDeptName: "",
        municipalDeptCode: "",
        municipalQzh: "",
        townDeptName: "",
        townDeptCode: "",
        townQzh: ""
      },
      ruleForm2: {
        check_name_id: ""
      },
      tableData: [],
      defaultChecks: [],
      selectData: [],
      addForm: {},
      basic: '',
      defaultChecked: []
    };
  },
  created() {
    this.getData();
    this.getInitData();
    this.getCheckTree()
  },
  methods: {
    // 市级部门开关变化处理
    handleMunicipalDeptChange(value) {
      if (!value) {
        // 开关关闭时，清空相关字段的验证错误
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate(['municipalDeptName', 'municipalDeptCode', 'municipalQzh']);
        });
      }
    },
    // 镇街部门开关变化处理
    handleTownDeptChange(value) {
      if (!value) {
        // 开关关闭时，清空相关字段的验证错误
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate(['townDeptName', 'townDeptCode', 'townQzh']);
        });
      }
    },
    handleClick(tab, event) {
      if (tab.name === "second") {
        this.localZjItemCode()
      }
    },
    submit() {
      // this.ruleForm.checkId = this.fourTestId
      this.fourCheckdialog.dialogVisible = false;
      // this.fourTestId = []
      // this.$refs.trees.setCheckedKeys([]);
    },
    // 选择检测配置项
    // handleSelectionChange(val){
    //   // this.fourTestId = null
    //   val.forEach((item)=>{
    //     this.fourTestId.push(item.id)
    //   })
    // },
    // 获取树型数据 // 修改为表结构数据
    getCheckTree() {
      this.query.pageNo = 0
      this.query.pageSize = 10
      FourChecks(this.query).then((res) => {
        this.fourTestTableData = res.data
        // 循环配置表存储id数组
        debugger
        let ids = []
        this.fourTestTableData.forEach((item) => {
          ids.push(item.id)
        })
        this.ruleForm.checkId = ids
      });
    },
    changeSelect(data, check) {
      this.checkedNodes = [];
      check.checkedNodes.forEach((item) => {
        if (item.id) {
          this.checkedNodes.push(item.id);
        }
      });
    },
    handleNodeClick(data) {
      let arr = this.$refs.trees.getCheckedNodes();
    },
    handOpenFourCheckDialog() {
      this.fourCheckdialog.dialogVisible = true;
    },
    //获取情形
    handleSituation(index, row) {
      this.ruleForm = row;
      this.materialCatalogueDialog.dialogVisible = true;
    },
    handleCloseFourCheck() {
      this.fourCheckdialog.dialogVisible = false;
      this.checkedNodes = [];
    },
    handleMetadata() {
      this.selectMetadata.dialogVisible = true;
      // this.$router.push('/selects')
    },
    dataSync(index, row) {
      this.ruleForm = row;
      this.ruleForm.syncTime = "* 0/20 * * * ?";
      this.ruleForm.isNeed = 1;
      this.syncDialog.dialogVisible = false;
      this.getCheckTree()

    },
    syncItemCode() {
      if (!this.ruleForm.taskCode) {
        this.$message.warning("事项编码不能为空!");
        return;
      }
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "正在同步数据中...", //显示在加载图标下方的加载文案
        background: "rgba(0, 0, 0, 0.8)",
        target: document.querySelector("#el-table"), //loadin覆盖的dom元素节点
      });
      itemCodeApi
          .syncItemCode({itemCode: this.ruleForm.taskCode, bmbm: this.bmbm})
          .then((a) => {
            this.syncTableData = a.data;
            this.syncDialog.dialogVisible = true;
            loading.close();
          })
          .catch((a) => {
            loading.close();
          });
    },
    //加载本地镇街数据
    localZjItemCode() {
      /*const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "正在加载数据中...", //显示在加载图标下方的加载文案
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });*/
      itemBusinessApi.localZjItemCode({bmbm: this.ruleForm.tongYiCode, itemCode: this.ruleForm.taskCode}).then(res => {
        if (res.code === 200) {
          // this.$forceUpdate()
          this.zjItemCodeData = res.data;
          if (res.data.length === 0) {
            this.zjItemCodeData = []
            // this.$message.info("无数据")
          }
          // loading.close()
        }
      })
    },
    handleEditZjItemCode(index, row) {
      this.ruleForm2 = JSON.parse(JSON.stringify(row));
      this.editZjItemCode.dialogVisible = true
    },
    saveZjItemCode(ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (valid) {
          this.ruleForm.endTime = null;
          const loading = this.$loading({
            lock: true, //lock的修改符--默认是false
            text: "正在处理数据中...", //显示在加载图标下方的加载文案
            target: document.querySelector(".el-table"), //loadin覆盖的dom元素节点
          });
          itemBusinessApi.saveZjItemCode(this.ruleForm2).then((a) => {
            if (a.code === 200) {
              this.$message.success("保存成功");
            } else {
              this.$message.error(a.msg);
            }
            this.localZjItemCode()
          }).catch(err => {
            this.$message.error("网络异常,请稍后重试");
          });
          this.editZjItemCode.dialogVisible = false
          loading.close();
        } else {
          return false;
        }
      });
    },
    deleteZjItemCode(index, row) {
      this.$confirm("此操作将永久删除该事项, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            itemBusinessApi.deleteZjItemCode(row).then(res => {
              if (res.code === 200) {
                this.$message.success("删除成功")
              } else {
                this.$message.success("删除失败")
              }
              this.localZjItemCode()
            })
          })
    },
    //新增事项
    handleAdd() {
      this.ruleForm = {};
      this.ruleForm.isNeed = 1;
      this.ruleForm.tongYiCode = this.bmbm;
      this.ruleForm.deptName = this.bmName;
      this.dialog.title = "新增事项";
      this.dialog.dialogVisible = true;
    },
    //初始话基础数据
    getInitData() {
      Promise.all([
        new Promise((re, rp) => {
          retentionperiodApi.list().then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          classTypeApi.list().then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          dataSetApi.getData({code: "syncTime"}).then((a) => {
            re(a);
          });
        }),
      ]).then((a) => {
        this.retentionperiod = a[0].data;
        this.cclassType = a[1].data;
        this.syncTimeData = a[2].data;
      });
    },

    search() {
      if (this.query.name !== undefined || this.query.itemCode !== undefined) {
        this.query.pageNo = 0;
      }
      this.getData();
    },
    handleEdit(index, row) {
      localStorage.setItem('taskCode', row.taskCode)
      this.dialog.title = "编辑";
      this.startTimeAndEndTime[0] = row.startTime;
      this.startTimeAndEndTime[1] = row.endTime;
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.ruleForm.itemPattern = row.itemPattern.toString()
      this.getCheckTree()
      this.activeName = "first";
      this.ruleForm.syncTime = row.syncTime ? row.syncTime : "* 0/20 * * * ?";
      this.dialog.dialogVisible = true;
    },
    handleClose(ruleForm) {
      this.form = {};
      this.dialog.dialogVisible = false
      this.materialCatalogueDialog.dialogVisible = false;
      this.$refs.ruleForm.clearValidate()


    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该事项, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            itemCodeApi.itemCodeRemove({id: row.id}).then((a) => {
              if (a.code == 200) {
                this.$message.success("删除成功");
                // this.query.pageNo = 0;
                this.getData();
              } else {
                this.$message.error(a.msg);
              }
            });
          })
          .catch(() => {
          });
    },
    cancel(formName) {
      // this.$refs[formName].resetFields();
      this.ruleForm = {};
      this.form = {};
      this.dialog.dialogVisible = false;
      this.materialCatalogueDialog.dialogVisible = false;
      this.$refs.ruleForm.clearValidate()
      this.syncTableData = []
      // this.syncTableData2 = []
    },
    //保存事项
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.ruleForm.endTime = null;
          const loading = this.$loading({
            lock: true, //lock的修改符--默认是false
            text: "正在数据数据中...", //显示在加载图标下方的加载文案
            target: document.querySelector(".el-table"), //loadin覆盖的dom元素节点
          });
          itemCodeApi.itemCodeSave(this.ruleForm).then((a) => {
            if (a.code === 200) {
              this.$message.success("保存成功");
              this.fourTestId = [];
              this.cancel(formName);
              this.getData();
            } else {
              this.$message.error(a.msg);
              this.fourTestId = [];
            }
            loading.close();
          }).catch(err => {
            this.$message.error("网络异常,请稍后重试");
            loading.close()
          });
        } else {
          return false;
          //     if (this.syncTableData1.length === 0) {
          //       let taskCode = this.ruleForm.taskCode;
          //       this.$refs[formName].resetFields();
          //       this.ruleForm.taskCode = taskCode
          //       this.$alert('请先同步事项数据', '提示', {
          //         confirmButtonText: '确定',
          //         type: 'warning'
          //       }).then(() => {
          //         return false;
          //       }).catch(() => {
          //         return false;
          //       });
          //     } else {
          //       const obj = this.syncTableData1[0];
          //       let taskCode = this.ruleForm.taskCode;
          //       if (obj.taskCode !== taskCode) {
          //         this.$refs[formName].resetFields();
          //         this.ruleForm.taskCode = taskCode
          //         this.$alert('请先同步事项数据', '提示', {
          //           confirmButtonText: '确定',
          //           type: 'warning'
          //         }).then(() => {
          //           return false;
          //         }).catch(() => {
          //           return false;
          //         });
          //       }
        }
      });
    },
    getData() {
      itemCodeApi.page(this.query).then((a) => {
        if (a.code == 200) {
          this.tableData = a.data;
          this.query.total = a.totalCount;
        }
      });
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.getData();
    },
    getIds(ids) {
      this.ids = ids;
      // console.log(this.ids,7777 )
    },
    cancelSyncItemCodeDialog() {
      this.syncTableData = []
      this.syncDialog.dialogVisible = false
    },
    removeForm(ruleForm) {
      this.addDialogVisible = false;
      this.$refs.ruleForms.resetFields();
    },
    handleClose2(ruleForm) {
      this.selectMetadata.dialogVisible = false
      // this.$refs['ruleForm'].resetFields()
    },
    addSaveIds(ruleForms) {
      this.$refs["ruleForms"].validate((valid) => {
        if (valid) {
          this.addForm.items_code = localStorage.getItem("taskCode");
          this.addForm.name = this.$store.state.name
          console.log(this.addForm.name, 888)
          addMatedate(this.addForm).then((res) => {
            if (res.code == 200) {
              this.$message({
                message: "添加成功",
                type: "success",
              });
              this.addForm = {};
              this.addDialogVisible = false;
              this.$refs.ruleForms.resetFields();
              getMatedateAll({
                itemsCode: localStorage.getItem('taskCode'),
                name: this.$store.state.name
              }).then((res) => {
                this.$store.commit('getData', res.data);
                //  this.$store.dispatch("getData", res.data)
              });

            } else {
              this.$message({
                message: "添加失败",
                type: "warning",
              });
              this.addForm = {};
              this.addDialogVisible = false;
              this.$refs.ruleForms.resetFields();
            }
          });
        }
      });
    },
    saveIds(ruleForm) {
      // this.selectMetadata.dialogVisible = false;
      this.addDialogVisible = true;
    },
    selectItemCodeFourChecked() {
      selectItemCodeFourChecked({taskCode: this.ruleForm.taskCode}).then(res => {
        if (res.code === 200) {
          if (res.data !== null) {
            const arr = res.data.four_setting_name.split(",");
            // this.$nextTick(() => {
            let that = this
            setTimeout(function () {
              arr.forEach(value => {
                that.$refs.trees.setChecked(value, true, false);
              });
            }, 500);
          } else {
            const checked = [];
            this.treeData.forEach(item => {
              // console.log(checked)
              item.children.forEach(a => {
                // console.log(a)
                checked.push(a.id);
              })
            })
            let that = this
            setTimeout(function () {
              checked.forEach(value => {
                that.$refs.trees.setChecked(value, true, false);
              });
            }, 500)
          }
        }
      })
    },

  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}

.searchDiv {
  margin-top: 10px;
}

/deep/ .el-dialog__headerbtn:hover .el-dialog__close {
  color: #fff;
}

/deep/ .el-dialog__headerbtn .el-dialog__close {
  color: #fff;
}

/deep/ .el-input-group__append > .syncItemBtn {
  background-color: #409eff;
  color: white;
  border-top: 3px;
  /* margin-left: 0px; */

}

/deep/ .el-input-group__append {
  margin-bottom: 6px;
  margin-top: 6px;
  border-top: 3px;
  border-radius: 2px;
}

/deep/ .configureDialog {
  /*min-width: 900px !important;*/
  /*margin-top: 0px !important;*/
  /*margin-left: 0px !important;*/
}

/deep/ .configureTree .el-tree-node > .el-tree-node__children {
  border: 1px solid #E5E2E2 !important;
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.1);
  background-color: #fff !important;
}

/deep/ .configureTree .el-tree-node__content {
  background-color: #F7FBFF;
  padding: 5px;
}

/deep/ .configureTree .el-tree-node > .el-tree-node__children .el-tree-node__content {
  background-color: #fff !important;
}

/deep/ .configureForm {
  height: calc(80vh - 180px);
}
</style>

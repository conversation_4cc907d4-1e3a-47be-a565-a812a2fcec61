<template>
  <div>
    <el-table :data="data" stripe style="width: 100%" :header-cell-style="{ background: '#F6F7FA' }" height="400px">
      <el-table-column
        prop="setting_name"
        label="元数据标签名"
        width="180"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="note"
        label="元数据标题"
        width="180"
        align="center"
      >
      </el-table-column>
      <el-table-column prop="defaule_values" label="来源" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.defaule_values == '0'">一体化</span>
          <span v-if="scope.row.defaule_values == '1'">自定义</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            type="danger"
            icon="el-icon-delete"
            circle
            v-if="scope.row.defaule_values == '1'"
            @click="reomve(scope.row.infoId)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getAll } from "../api/archivesAttributeApi";
import { findAllByItemCode } from "../api/itemAttribute";
import { getMatedateAll, deleteMatedate } from "../api/first";
import "../store/index.js";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      // data: [],
      defaultProps: {
        children: "children",
        label: "name",
      },
      ids: [], //存放节点
      // val: "0",
    };
  },
  watch: {
    data: function (val) {
      console.log(val, "子组件-------------------");
      // this.data = val
    },
  },
  created() {
    // this.getData();
    // this.getMatedateA();
    // console.log(this.$store.state.data)
  },

  mounted() {
    let timer = setInterval(() => {
      if (this.$store.state.data.length > 0) {
        this.data = this.$store.state.data;
      }
    }, 300);
    // clearInterval(timer);
  },

  methods: {
    getMatedateA() {
      getMatedateAll({ itemsCode: localStorage.getItem("taskCode"),name:this.$store.state.name }).then(
        (res) => {
          console.log(res);
          this.data = res.data;
          this.$store.commit("getData", res.data);
        }
      );
    },
    handleCheckChange(data, checked, indeterminate) {
      console.log("XMLTreeIds====" + this.ids);
      this.$emit("ids", this.$refs.tree.getCheckedKeys());
    },

    reomve(id) {
      deleteMatedate({ id: id }).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: "删除成功",
            type: "success",
          });
          this.getMatedateA();
        }
      });
    },
    getData() {
      getAll({ type: this.type }).then((res) => {
        this.data = res.data;
      });
      // findAllByItemCode({ itemCode: this.taskCode }).then((res) => {
      //   console.log(res.data);
      //   let arr = [];
      //   res.data.forEach((v) => {
      //     arr.push(v.archivesAttributeId);
      //   });
      //   let that = this;
      //   setTimeout(function () {
      //     arr.forEach((value) => {
      //       that.$refs.tree.setChecked(value, true, false);
      //     });
      //   }, 500);
      // });
    },
  },
};
</script>

<style lang="scss" scoped></style>

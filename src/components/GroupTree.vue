<template>
  <div class="mote">
    <el-card
        style="
        margin-top: 10px;
        padding: 0px 10px;
        width: 20%;
        height: 920px;
        overflow-y: auto;
      "
    >
      <el-input
          placeholder="输入关键字进行过滤"
          v-model="filterText"
          size="small"
      ></el-input>
      <el-tree
          class="filter-tree"
          :data="data"
          highlight-current
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
          ref="tree"
      >
      </el-tree>
    </el-card>
    <el-card
        v-if="this.openCard"
        style="width: 80%; margin-top: 10px; margin-left: 10px"
    >
      <el-button
          type="primary"
          @click="addList"
          v-if="this.tableData.length < 1"
      >添加
      </el-button>
      <el-table :data="tableData" style="width: 100%; margin-top: 10px" border>
        <el-table-column prop="outUserId" label="用户ID" align="center"/>
        <el-table-column
            prop="dzyzAccount"
            label="电子印章账户"
            align="center"
        />
        <!-- <el-table-column prop="address" label="镇街名字"> </el-table-column> -->
        <el-table-column prop="tyshxydm" label="统一社会信用代码" align="center"/>
        <el-table-column prop="createDate" label="创建日期" align="center"/>
        <el-table-column prop="address" label="操作" align="center">
          <template slot-scope="scope">
            <el-button
                type="text"
                icon="el-icon-edit"
                @click="edit(scope.row)"
            >编辑
            </el-button>
            <el-button
                type="text"
                icon="el-icon-star-off"
                @click="star(scope.row)"
            >查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="25%"
        :before-close="handleClose"
        center
    >
      <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="110px"
          class="demo-ruleForm"
      >
        <el-form-item label="用户ID" prop="outUserId">
          <el-input
              v-model="ruleForm.outUserId"
              placeholder="请输入用户ID"
          ></el-input>
        </el-form-item>
        <el-form-item label="电子印章账户" prop="dzyzAccount">
          <el-input
              v-model="ruleForm.dzyzAccount"
              placeholder="请输入电子印章账户"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')"
          >确定
          </el-button>
          <el-button @click="resetForm('ruleForm')">取消</el-button>
        </el-form-item>
      </el-form>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span> -->
    </el-dialog>
    <el-dialog
        title="水印"
        :visible.sync="ifmarsdialogVisible"
        width="100%"
        center
        :fullscreen="true"
        :before-close="handleClose"
    >
      <iframe :src="src" frameborder="0" width="100%" height="800px"></iframe>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  getAll,
  signatureManage,
  getAdd,
  genFileWaterSignatureFilePath,
  removeTestFile,
} from "../api/signatureManage";

export default {
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  data() {
    return {
      type: 0,
      filterText: "",
      data: [],
      src: '',
      defaultProps: {
        children: "children",
        label: "groupName",
      },
      dialogVisible: false,
      ifmarsdialogVisible: false,
      openCard: false,
      title: "",
      tableData: [],
      ruleForm: {
        outUserId: "",
        dzyzAccount: "",
      },
      dataForm: {
        type: "",
        tyshxydm: "",
      },
      rules: {
        outUserId: [
          {required: true, message: "请输入用户ID", trigger: "blur"},
        ],
        dzyzAccount: [
          {required: true, message: "请输入电子印章账号", trigger: "blur"},
        ],
      },
    };
  },
  created() {
    this.getData();
  },
  methods: {
    star(row) {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      genFileWaterSignatureFilePath(row).then((res) => {
        if (res.code == 200) {
          this.src = process.env.VUE_APP_TESTFILE_API + "/" + res.data;
          this.ifmarsdialogVisible = true
        }
        this.$message.error(res.msg)
        loading.close();

      });
    },
    handleClose() {
      removeTestFile()//移除测试文件
      this.ifmarsdialogVisible = false
      this.dialogVisible = false
      this.src = ""
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.groupName.indexOf(value) !== -1;
    },
    edit(row) {
      this.title = "编辑";
      this.ruleForm = row;
      this.dialogVisible = true;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          getAdd(this.ruleForm).then((res) => {
            if (res.code == 200) {
              this.$message({
                message: res.msg,
                type: "success",
              });
              signatureManage({
                tyshxydm: this.ruleForm.tyshxydm,
                type: this.ruleForm.type,
              }).then((res) => {
                this.tableData = res.data;
              });
            } else {
              this.$message({
                message: res.msg,
                type: "warning",
              });
            }
          });
        } else {
          return false;
        }
      });
      this.dialogVisible = false;
      this.$refs[formName].resetFields();
    },
    resetForm(formName) {
      this.dialogVisible = false;
      this.$refs[formName].resetFields();
    },
    addList() {
      this.dialogVisible = true;
      this.title = "添加";
    },
    handleNodeClick(data) {
      this.ruleForm.type = data.type;
      this.ruleForm.tyshxydm = data.extOrgTyshxydm;
      if (this.ruleForm.tyshxydm) {
        this.openCard = true;
        signatureManage({
          tyshxydm: data.extOrgTyshxydm,
          type: data.type,
        }).then((res) => {
          if (res.code == 200) {
            this.tableData = res.data;
          }
        });
      } else {
        this.openCard = false;
      }
    },
    getData() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      getAll()
          .then((a) => {
            this.data = a.data;
            loading.close();
          })
          .catch((a) => {
            //成功回调函数停止加载
            loading.close();
          });
    },
  },
};
</script>
<style scoped>
.mote {
  display: flex;
  height: 100%;
}

/deep/
.el-tree--highlight-current
.el-tree-node.is-current
> .el-tree-node__content {
  background-color: wheat !important;
}
</style>

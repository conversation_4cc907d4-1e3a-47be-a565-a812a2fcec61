<template>
  <div class="box">

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="申请材料" name="first">
        <div class="searchDiv">
          <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
            <el-form-item label="档案材料名称">
              <el-input v-model="query.archName" placeholder="档案材料名称"></el-input>
            </el-form-item>
            <el-form-item label="事项材料名称">
              <el-input v-model="query.businessName" placeholder="事项材料名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" plain @click="handleAdd">添加</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSync">同步申请材料</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="tableData" style="width: 100%" height="580" :span-method="objectSpanMethod"
                  :cell-style="columnStyle" :header-cell-style="{ background: '#F6F7FA' }">
          <el-table-column prop="tycl" label="材料类型" align="center" width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.tycl == '1'">通用材料</span>
              <span v-else-if="scope.row.tycl == '0'">非通用材料</span>
              <span v-else-if="scope.row.tycl == '2'">自定义材料</span>
              <span v-else>其他</span>
            </template>
          </el-table-column>
          <el-table-column label="序号" align="center" prop="index">
            <!-- <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template> -->
          </el-table-column>
          <el-table-column prop="archFileName" label="档案材料名称" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.archFileName == null">无</span>
              {{ scope.row.archFileName }}
            </template>
          </el-table-column>
          <el-table-column prop="materialName" label="事项材料名称" align="center"></el-table-column>
          <el-table-column prop="source" label="材料来源" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.source == 0">一体化</span>
              <span v-else-if="scope.row.source == 1">其他</span>
              <span v-else>用户</span>
            </template>
          </el-table-column>
          <el-table-column prop="fileNameAlias" label="文件别名" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.fileNameAlias == null">无</span>
              {{ scope.row.fileNameAlias }}
            </template>
          </el-table-column>
          <el-table-column prop="zcqy" label="是否容缺" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.zcqy == 0">否</el-tag>
              <el-tag v-else-if="scope.row.zcqy == 1" type="danger"> 是</el-tag>
              <el-tag v-else type="primary">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="zipPackage" label="所属目录" align="center">
            <template slot-scope="scope">
          <span v-if="scope.row.zipPackage != null">{{
              scope.row.zipPackage.name
            }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="rangId" label="目录范围" align="center">
                    <template slot-scope="scope">
                       <el-tag >{{scope.row.randId}}</el-tag>
                    </template>
                </el-table-column>   -->
<!--          <el-table-column prop="isResult" label="存放结果信息描述" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.isResult == 0" type="info">不存放</el-tag>
              <el-tag v-else-if="scope.row.isResult == 1" type="success">存放
              </el-tag>
              <el-tag v-else type="info">不存放</el-tag>
            </template>
          </el-table-column>-->
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" @click="handleEdit(scope.$index, scope.row)">编辑
              </el-button>
              <el-button type="text" icon="el-icon-delete" @click="handleRemove(scope.$index, scope.row)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--  分页-->
        <el-pagination v-model:page-size="query.pageSize" :page-sizes="[20,30,50,100]"
                       layout="total, sizes, prev, pager, next, jumper" :total="query.total"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange" style="float: right;margin-top: 10px;"/>
        <el-dialog :title="dialog.title" :visible.sync="dialog.dialogVisible" width="50%" :before-close="handleClose"
                   :append-to-body="true" center :close-on-click-modal="true">
          <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" size="small" label-width="auto"
                   class="demo-ruleForm">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="档案材料名称/别名" prop="archFileName">
                  <el-input v-model="ruleForm.archFileName" autocomplete="off"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事项材料名称" prop="materialName">
                  <el-input v-model="ruleForm.materialName" autocomplete="off" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="来源" prop="source">
                  <el-select v-model="ruleForm.source" clearable filterable placeholder="请选择" style="width: 100%">
                    <el-option v-for="item in dataSet" :key="item.dvalue" :label="item.dname" :value="item.dvalue">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="目录归属" prop="zipPackageID">
                  <el-select v-model="ruleForm.zipPackageID" clearable filterable placeholder="请选择"
                             style="width: 100%">
                    <el-option v-for="item in zipPackage" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--          <el-col :span="12">
                          <el-form-item label="材料密级" prop="classificationType">
                            &lt;!&ndash; v-model="ruleForm.classificationType" &ndash;&gt;
                            <el-select
                            v-model="ruleForm.classificationType"
                              clearable
                              filterable
                              placeholder="请选择"
                              style="width: 100%"
                            >
                              <el-option
                                v-for="item in classificationType"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                              >
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>-->

              <!--          <el-col :span="12">
                          <el-form-item label="保管期限" prop="retentionPeriod">
                            &lt;!&ndash; v-model="ruleForm.retentionPeriod" &ndash;&gt;
                            <el-select
                            v-model="ruleForm.retentionPeriod"
                              clearable
                              filterable
                              placeholder="请选择"
                              style="width: 100%"
                            >
                              <el-option
                                v-for="item in retentionPeriod"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                              >
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>-->
              <!-- <el-col :span="12">
                            <el-form-item label="目录范围"  prop="randId">
                                <el-select v-model="ruleForm.randId" clearable filterable placeholder="请选择" style="width:100%">
                                    <el-option
                                    v-for="item in randes"
                                    :key="item.dvalue"
                                    :label="item.dname"
                                    :value="item.dvalue">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col> -->
              <el-col :span="12">
                <el-form-item label="材料类型" prop="tycl">
                  <el-select v-model="ruleForm.tycl" clearable filterable placeholder="请选择" style="width: 100%">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="顺序" prop="sequence">
                  <el-input-number v-model="ruleForm.sequence" :min="1" :max="10" label="顺序"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否容缺" prop="zcqy">
                  <el-switch v-model="ruleForm.zcqy" active-color="#13ce66" inactive-color="#ff4949" :active-value="`1`"
                             :inactive-value="`0`"></el-switch>
                </el-form-item>
              </el-col>
<!--              <el-col :span="8">
                <el-form-item label="是否转版式文件" prop="isFormat">
                  <el-switch v-model="ruleForm.isFormat" active-color="#13ce66" inactive-color="#ff4949"
                             :active-value="1"
                             :inactive-value="1"></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="存放结果信息描述" prop="isResult">
                  <el-switch v-model="ruleForm.isResult" active-color="#13ce66" inactive-color="#ff4949"
                             :active-value="1"
                             :inactive-value="0"></el-switch>
                </el-form-item>
              </el-col>-->

            </el-row>
          </el-form>
          <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </span>
        </el-dialog>
      </el-tab-pane>
      <el-tab-pane label="审批文书" name="second">
        <div class="searchDiv">
          <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
            <el-form-item label="模板名称">
              <el-input v-model="query.paperTemplatesName" placeholder="模板名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSyncLocalPapers">同步审批文书</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="localPapersData" style="width: 100%" height="550"
                  :header-cell-style="{ background: '#F6F7FA' }">
          <el-table-column label="序号" align="center" prop="index">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="paperDealNode" label="处理环节" align="center"
                           :formatter="(row, column) => $getDict(row, column, 'paperDealNode')">
          </el-table-column>
          <el-table-column prop="paperDealAction" label="处理动作" align="center"
                           :formatter="(row, column) => $getDict(row, column, 'paperDealAction')">
          </el-table-column>
          <el-table-column prop="paperDealType" label="文书类型" align="center"
                           :formatter="(row, column) => $getDict(row, column, 'paperDealType')">
          </el-table-column>
          <el-table-column prop="paperTemplatesName" label="文书模板名称" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.paperTemplatesName == null">无</span>
              {{ scope.row.paperTemplatesName }}
            </template>
          </el-table-column>
          <el-table-column prop="zipPackage" label="所属目录" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.zipPackage != null">{{ scope.row.zipPackage.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" @click="handleLocalPapersEdit(scope.$index, scope.row)">编辑
              </el-button>
              <el-button type="text" icon="el-icon-delete" @click="handleRemoveLocalPapers(scope.$index, scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--    审批文书  -->
        <el-dialog :title="localPapersDialog.title" :visible.sync="localPapersDialog.dialogVisible" width="50%"
                   :append-to-body="true" center :close-on-click-modal="true">
          <el-form :model="localPapersForm" status-icon :rules="localPapersFormRule" ref="localPapersForm" size="small"
                   label-width="auto"
                   class="demo-ruleForm">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="文书模板名称" prop="paperTemplatesName">
                  <el-input v-model="localPapersForm.paperTemplatesName" autocomplete="off" readonly></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="目录归属" prop="zippackageId">
                  <el-select v-model="localPapersForm.zippackageId" clearable filterable placeholder="请选择"
                             style="width: 100%">
                    <el-option v-for="item in zipPackage" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveLocalPapers('localPapersForm')">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </span>
        </el-dialog>

      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
import { materialsBillApi } from "../api/materialsBill";
import { zipPackageApi } from "../api/zipPackage";
import { dataSetApi } from "../api/dataSet";
import { classTypeApi } from "../api/classType";
import {retentionperiodApi} from "../api/retentionperiod";
import {localPapersPage, syncLocalPapers, removeLocalPapers, saveLocalPapers} from "../api/itemInfoLocalPapers";
export default {
  props: {
    itemCode: String,
    taskType: String,
    unSid: String,
    versionId: String,
  },
  data() {
    var archFileName = (rule, value, callback) => {
      if (!value && !this.ruleForm.materialName) {
        return callback(new Error("材料名称不能为空!"));
      } else {
        return callback();
      }
    };
    return {
      activeTab: 'first',
      classificationType: [], //材料密级
      retentionPeriod: [], //保管期限
      zipPackage: [],
      dataSet: [],
      query: {
        unSid: this.unSid,
        versionId: this.versionId,
        itemId: this.itemCode,
        pageNo: 0,
        pageSize: 20,
        total: 0,
      },
      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      localPapersDialog: {
        title: "审批文书",
        dialogVisible: false,
      },
      materialCatalogueDialog: {
        title: "新增",
        dialogVisible: false,
      },
      rules: {
        source: [
          {required: true, message: "请选择材料来源", trigger: "blur"},
        ],
        randId: [
          {required: true, message: "请选择目录范围", trigger: "change"},
        ],
        archFileName: [{validator: archFileName, trigger: "blur"}],
        zipPackageID: [
          {required: true, message: "请选择文件目录", trigger: "blur"},
        ],
      },
      localPapersFormRule: {
        zippackageId: [
          {required: true, message: "请选择文件目录", trigger: "blur"},
        ],
      },
      ruleForm: {
        zcqy: 0,
      },
      localPapersForm: {},
      ruleForm2: {
        zcqy: 0,
        archFileName: '',
        materialName: '',
        source: '',
        zipPackageID: ''
      },
      //范围
      randes: [],
      tableData: [],
      localPapersData: [],
      options: [{
        value: '0',
        label: '非通用材料'
      }, {
        value: '1',
        label: '通用材料'
      }, {
        value: '2',
        label: '自定义材料'
      }, {
        value: '3',
        label: '其他'
      }],
      dataList: [],//存放表格数据
      spanArr: [],//存放整合数据
      position: 0//记录下标
    };
  },
  created() {
    this.getData();
    this.getInitData();
  },
  methods: {
    //同步固化文件
    handleSolidify() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      materialsBillApi.syncSolidify({ itemCode: this.itemCode }).then((a) => {
        let tycl1 = []
        let tycl2 = []
        let tycl3 = []
        let tycl4 = []
        let arr = a.data;
        arr.forEach((item, index) => {
          if (item.tycl == '0') {
            tycl1.push(item)
          } else if (item.tycl == '1') {
            tycl2.push(item)
          } else if (item.tycl == '2') {
            tycl3.push(item)
          } else if( (item.tycl == '3')){
            tycl4.push(item)
          }
        })
        this.tableData = [...tycl1, ...tycl2, ...tycl3, ...tycl4]
        this.rowspan()
        this.$message.success("同步成功");
        loading.close();
      });
    },
    getRangInfo(val) {
      if (this.randes.filter((a) => a.dvalue == val).length <= 0) {
        return "未知";
      }
      return this.randes.filter((a) => a.dvalue == val)[0].dname;
    },
    handleTabClick(tab, event) {
      if (tab.name == 'first') {
        this.getData();
      }
      if (tab.name == 'second') {
        this.getLocalPapers();
      }
    },
    saveLocalPapers(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          saveLocalPapers(this.localPapersForm).then((res) => {
            if (res.code === 200) {
              this.$message.success(res.msg);
              this.localPapersDialog.dialogVisible = false;
              this.getLocalPapers();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleAdd() {
      this.ruleForm = {};
      this.dialog.title = "新增";
      this.dialog.dialogVisible = true;
    },
    handleSync() {
      this.$confirm("是否同步材料数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const loading = this.$loading({
          lock: true, //lock的修改符--默认是false
          text: "正在同步数据中...", //显示在加载图标下方的加载文案
          target: document.querySelector(".el-tabs"), //loadin覆盖的dom元素节点
        });
        materialsBillApi
            .syncMaterialBill({itemCode: this.itemCode, ywqxId: this.unSid, versionId: this.versionId})
            .then((a) => {
              if (a.code === 200) {
                this.$message.success("同步成功");
                this.getData()
              } else {
                this.$message.error("同步失败");
              }
              loading.close();
            }).catch(() => {
          loading.close();
        });
      });
    },
    handleSyncLocalPapers() {
      this.$confirm("是否同步材料数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const loading = this.$loading({
          lock: true, //lock的修改符--默认是false
          text: "正在同步数据中...", //显示在加载图标下方的加载文案
          target: document.querySelector(".el-tabs"), //loadin覆盖的dom元素节点
        });
        syncLocalPapers(this.query).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg);
            this.getLocalPapers();
          }
          loading.close();
        })
      })

    },
    handleRemoveLocalPapers(index, row) {
      this.$confirm("此操作将永久删除 " + row.paperTemplatesName + " 该信息, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        removeLocalPapers(row.id).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg);
            this.getLocalPapers();
          }
        })
      })
    },
    handleLocalPapersEdit(index, row) {
      this.localPapersDialog.title = "编辑审批文书";
      this.localPapersForm = JSON.parse(JSON.stringify(row));

      this.$nextTick(() => {
        this.$refs['localPapersForm'].clearValidate();
      })
      this.localPapersDialog.dialogVisible = true;
    },
    //初始话基础数据
    getInitData() {
      Promise.all([
        new Promise((rs, rp) => {
          zipPackageApi.list({itemCode: this.itemCode}).then((a) => {
            rs(a);
          });
        }),
        new Promise((rs, rp) => {
          dataSetApi.getData({code: "source"}).then((a) => {
            rs(a);
          });
        }),
        new Promise((rs, rp) => {
          dataSetApi.getData({ code: "cataRange" }).then((a) => {
            rs(a);
          });
        }),
        new Promise((rs, rp) => {
          classTypeApi.list().then((a) => {
            rs(a);
          });
        }),
        new Promise((rs, rp) => {
          retentionperiodApi.list().then((a) => {
            rs(a);
          });
        }),
      ]).then((a) => {
        this.zipPackage = a[0].data;
        this.dataSet = a[1].data;
        this.randes = a[2].data;
        this.classificationType = a[3].data;
        this.retentionPeriod = a[4].data;
      });
    },
    handleMaterial(index, row) {
      this.materialCatalogueDialog.dialogVisible = true;
    },
    search() {
      this.getData();
      this.getLocalPapers();
    },
    handleEdit(index, row) {
      this.dialog.title = "编辑";
      this.ruleForm = { ...row };
      this.ruleForm.isResult == null ? `0` : `1`;
      this.dialog.dialogVisible = true;
    },
    handleClose(done) {
      this.ruleForm = {};
      this.form = {};
      done();
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该信息, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            materialsBillApi.remove({ id: row.id }).then((a) => {
              if (a.code === 200) {
                this.$message.success("删除成功");
                this.query.pageNo = 0;
                this.getData();
              } else {
                this.$message.error("删除失败");
              }
            });
          })
          .catch(() => {
          });
    },
    cancel() {
      this.form = {};
      this.dialog.dialogVisible = false;
      this.localPapersDialog.dialogVisible = false;
    },
    save(formName) {
      if (!this.ruleForm.zcqy) {
        this.ruleForm.zcqy = 0;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.ruleForm.itemCodeID = this.itemCode; //事项号
          this.ruleForm.ywqxId = this.unSid; //情形号
          this.ruleForm.versionId = this.versionId; //版本号
          materialsBillApi.save(this.ruleForm).then((res) => {
            if (res.code === 200) {
              this.$message.success(res.msg);
              this.ruleForm = {};
              this.dialog.dialogVisible = false;
              this.getData();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      //row 需要合并的列索引
      // column 当前行的行数
      // rowIndex 当前列的数据
      // columnIndex 当前列的数据
      if (columnIndex === 0) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    columnStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        //第二三第四列的背景色就改变了2和3都是列数的下标
        if (row.tycl == '0') {
          //字体颜色
          return "background:red;color:#fff;"
        } else if (row.tycl == '1') {
          //字体颜色
          return "background:#409EFF;color:#fff;"
        } else if (row.tycl == '2') {
          return "background:#00FF00;color:#fff;"
        }
        return "background:#e6cc6f;color:#fff;";
      }

    },
    rowspan() {
      //每次调用清空数据
      this.spanArr = [];
      this.position = 0;
      let tycl1 = 0
      let tycl2 = 0
      let tycl3 = 0
      let tycl4 = 0
      //this.dataList存放表格数据数组
      this.tableData.forEach((item, index) => {
        if (item.tycl == '0') {
          tycl1 += 1
          item.index = tycl1
        } else if (item.tycl == '1') {
          tycl2 += 1
          item.index = tycl2
        } else if (item.tycl == '2') {
          tycl3 += 1
          item.index = tycl3
        } else {
          tycl4 += 1
          item.index = tycl4
        }
        if (index === 0) {
          this.spanArr.push(1);
          this.position = 0;
        } else {
          if (
              this.tableData[index].tycl === this.tableData[index - 1].tycl
          ) {

            this.spanArr[this.position] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.position = index;
          }
        }

      });

    },
    getData() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "正在加载数据中...", //显示在加载图标下方的加载文案
        target: document.querySelector("#el-table"), //loadin覆盖的dom元素节点
      });
      materialsBillApi
          .page(this.query)
          .then((a) => {
            if (a.code === 200) {
              let tycl1 = []
              let tycl2 = []
              let tycl3 = []
              let tycl4 = []
              let arr = a.data;
              arr.forEach((item, index) => {
                if (item.tycl == '0') {
                  tycl1.push(item)
                } else if (item.tycl == '1') {
                  tycl2.push(item)
                } else if (item.tycl == '2') {
                  tycl3.push(item)
                } else {
                  tycl4.push(item)
                }
              })
              this.tableData = [...tycl1, ...tycl2, ...tycl3, ...tycl4]
              this.query.pageNo = a.pageNo;
              this.query.pageSize = a.pageSize;
              this.query.total = a.totalCount;
              this.rowspan()

            }
            loading.close();
          })
          .catch((a) => {
            loading.close();
          });
    },
    getLocalPapers() {
      localPapersPage(this.query).then(res => {
        if (res.code === 200) {
          this.localPapersData = res.data;
        }
      })
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.getData();
    },
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}

.searchDiv {
  margin-top: 10px;
}
</style>

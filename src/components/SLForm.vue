<template>
  <div>
    <el-row :gutter="20" v-if="form != null">
      <el-form ref="form" :model="form" label-width="200px">
        <el-col :span="8">
          <el-form-item label="业务流水号">
            <el-input
                v-model="form.sblsh"
                placeholder="业务流水号"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办件类型">
            <el-input
                v-model="form.projecttype"
                placeholder="办件类型"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办件编号">
            <el-input
                v-model="form.projectno"
                placeholder="办件编号"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地方基本编码">
            <el-input
                v-model="form.localcatalogcode"
                placeholder="地方基本编码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人名称">
            <el-input
                v-model="form.applyername"
                placeholder="申请人名称"
            ></el-input>
          </el-form-item>
        </el-col> <el-col :span="8">
      </el-col>
        <el-col :span="8">
          <el-form-item label="申请人证件号码">
            <el-input
                v-model="form.applyerpagecode"
                placeholder="申请人证件号码"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="受理时间">
            <el-input
                v-model="form.applydate"
                placeholder="受理时间"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人/代理人姓名">
            <el-input
                v-model="form.contactname"
                placeholder="联系人/代理人姓名"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法定代表人">
            <el-input
                v-model="form.legal"
                placeholder="法定代表人"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理部门">
            <el-input
                v-model="form.orgname"
                placeholder="受理部门"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理人员">
            <el-input
                v-model="form.handleusername"
                placeholder="受理人员"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理时间" v-if="form.acceptdate">
            <el-input
                v-model="form.acceptdate"
                placeholder="受理时间"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理文书编号" v-if=form.acceptdocno>
            <el-input
                v-model="form.acceptdocno"
                placeholder="受理文书编号"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务发生事项部门名称" v-if="form.ywfsSxbmmc">
            <el-input
                v-model="form.ywfsSxbmmc"
                placeholder="业务发生事项部门名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务发生所在行政区划代码">
            <el-input
                v-model="form.regioncode"
                placeholder="业务发生所在行政区划代码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="不受理原因">
            <el-input
                v-model="form.bslyy"
                placeholder="不受理原因"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="镇街名称" v-if="form.hasTown">
            <el-input
                v-model="form.hasTown"
                placeholder="是否镇街"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="镇街名称" v-if="form.townName">
            <el-input
                v-model="form.townName"
                placeholder="镇街名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="承诺办结时间" v-if="form.promiseDate">
            <el-input
                v-model="form.promiseDate"
                placeholder="承诺办结时间"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人统一身份认证平台账号" v-if="form.applyUerIdCode">
            <el-input
                v-model="form.applyUerIdCode"
                placeholder="申请人统一身份认证平台账号"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-row :gutter="24" v-else>
      <div style="text-align: center">暂无数据</div>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
    };
  },
  created() {
  },

  mounted() {
  },

  methods: {
    save() {},
    cancel() {},
  },
};
</script>
<style scoped>
.text{
  font-size: 18px;
  margin-left: 60px;
  color: red;
}
</style>

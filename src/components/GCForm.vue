<template>
  <div>
    <el-row :gutter="20" v-for="(item,index) in form">
      <el-form :model="item" label-width="200px">
        <div style="margin-left: 20px;font-weight: bolder"></div>
        <div style="margin-left: 20px;font-weight: bolder">环节：{{ index + 1 }} 环节名称：{{ item.processname }}</div>
        <el-col :span="8">
          <el-form-item label="业务流水号">
            <el-input
                v-model="item.sblsh"
                placeholder="业务流水号"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经办人">
            <el-input
                v-model="item.handleusername"
                placeholder="经办人"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理部门所在地行政区域代码">
            <el-input
                v-model="item.regioncode"
                placeholder="办理部门所在地行政区域代码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理部门统一社会信用代码">
            <el-input
                v-model="item.orgcode"
                placeholder="办理部门统一社会信用代码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理部门名称">
            <el-input
                v-model="item.orgname"
                placeholder="办理部门名称"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="环节开始时间">
            <el-input
                v-model="item.eventstarttime"
                placeholder="环节开始时间"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="环节结束时间">
            <el-input
                v-model="item.eventEndTime"
                placeholder="环节结束时间"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理时间">
            <el-input
                v-model="item.eventEndTime"
                placeholder="办理时间"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理意见">
            <el-input
                v-model="item.handleexplain"
                placeholder="办理意见"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="意见类型">
            <el-input
                v-model="handleexplainType"
                placeholder="意见类型"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" v-if="form.remark">
            <el-input
                v-model="item.remark"
                placeholder="备注"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务系统名称">
            <el-input
                v-model="item.systemname"
                placeholder="业务系统名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" v-if="item.remark">
            <el-input v-model="item.remark" placeholder="备注"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据提供部门名称">
            <el-input
              v-model="item.prodataorgname"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="业务办理项编码">
            <el-input
              v-model="item.taskhandleitem"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据提供部门行政区划代码">
            <el-input
              v-model="item.prodataregioncode"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据提供部门统一社会信用代码">
            <el-input
              v-model="item.prodataorgcode"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-row :gutter="20" v-if="form.length == 0">
      <div style="text-align: center">暂无数据</div>
    </el-row>
  </div>
</template>

<script>
import {formatDate} from '@/utils/tool'
export default {
  props: {
    form: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      GCName:"过程",
      handleexplainType:"建议",
      labelPosition: "right",
    };
  },


};
</script>
<style scoped>
.title {
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
  padding-bottom: 10px;
  font-size: 16px;
  margin-left: 5px;
}
</style>

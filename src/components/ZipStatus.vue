<template>
  <div class="zipDiv">
    <el-row :gutter="10">
      <el-col :span="6">
        <div class="zipFirst">
          <div class="child" id="div_1_1">
            {{ form.archivesNo }}
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="zipFirst">
          <div class="child" id="div_2_1">
            {{ form.archivesNo }}
          </div>
          <div class="child" id="div_2_2">归档信息包签名文件.pdf</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="zipFirst" ref="material">
          <div class="basic child" id="basic">基本信息.xml</div>
          <div class="process child" id="process">办理流程.xml</div>
          <div class="file child" id="file">归档配置信息.xml</div>
          <div class="result child" id="result">结果信息描述.xml</div>
          <div
              class="child"
              :id="`div_3_` + (index + 1)"
              v-for="(item, index) in catalogs"
              :key="index"
          >
            {{ item.name }}
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="zipFirst" ref="file">
          <div
              class="child"
              :id="`div_4_` + (index + 1)"
              v-for="(item, index) in files2"
              :key="index"
          >
            {{ item }}
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import LeaderLine from "leader-line";
import {archivesTypeApi} from "../api/archivesType";
import {materialsBillApi} from "../api/materialsBill";
import {tool} from "../utils/tool";

export default {
  props: {
    sblsh: {
      type: String,
      default: () => "",
    },
    form: {
      type: Object,
      default: () => {
      },
    },
    itemCode: {
      type: String,
      default: () => "",
    },
    files: {
      type: Array,
      default: () => [],
    },
    stage: {
      type: String,
      default: () => "0",
    },
  },
  data() {
    return {
      newLineArray: [],
      catalogsLineArray: [],
      files2: [],
      fileArray: [],
      newStageValue: 0,
      catalogs: [],
      lineArray: [],
      styleOption: {
        color: "blue", // 指引线颜色
        endPlug: "disc", // 指引线结束点的样式 hand,disc
        size: 1, // 线条尺寸
        startSocket: "right", //在指引线开始的地方从元素左侧开始
        endSocket: "left", //在指引线开始的地方从元素右侧结束
        hide: false, // 绘制时隐藏，默认为false，在初始化时可能会出现闪烁的线条
        startPlugColor: "black", // 渐变色开始色
        endPlugColor: "black", // 渐变色结束色
        gradient: true, // 使用渐变色
        outLineColor: "blue",
        path: "grid", // straight,arc,fluid,magnet,grid
        dash: {
          // 虚线样式
          animation: false, // 让线条滚动起来
        },
        zIndex: 9999,
        hide: false,
      },
    };
  },
  created() {
    console.log(this.itemCode)
    // debugger
    let retenName = "";
    if (this.form.retenName.lastIndexOf("年") > 0) {
      retenName = this.form.retenName.replace("年", "");
    } else if (this.form.retenName.lastIndexOf("永久") > 0) {
      retenName = this.form.retenName.replace("永久", "");
    }
    let name = this.form.type + retenName;
    if (this.form.isFile == 0) {
      this.form.fileStatusName = "未采集";
    } else if (this.form.isFile == 1) {
      this.form.fileStatusName = "已采集";
    } else if (this.form.isFile == 2) {
      this.form.fileStatusName = "已生成信息包";
    }
  },
  watch: {
    stage: function (val) {
      this.newStageValue = val;
      this.getReadyFiles();
    },
  },
  destroyed() {
    this.lineArray.forEach((a) => {
      a.remove();
    });
    this.newLineArray.forEach((a) => {
      a.remove();
    });
    this.catalogsLineArray.forEach((a) => {
      a.remove();
    });
  },
  methods: {
    initLine() {
      let that = this;
      this.$nextTick((a) => {
        //预归档div
        let start = document.getElementById("div_1_1");
        //电子文件号div
        let end = document.getElementById("div_2_1");
        let startLine = new LeaderLine(start, end, that.styleOption);
        that.lineArray.push(startLine);
        //pdfdiv
        let end2 = document.getElementById("div_2_2");
        let startLine2 = new LeaderLine(start, end2, that.styleOption);
        that.lineArray.push(startLine2);

        for (
            let index = 0;
            index < that.$refs.material.children.length;
            index++
        ) {
          let obj = that.$refs.material.children[index];
          that.lineArray.push(
              new LeaderLine(
                  end,
                  document.getElementById(obj.id),
                  that.styleOption
              )
          );
        }

        that.showLine();
      });
    },
    showLine() {
      this.$nextTick(() => {
        let lineArray2 = document.getElementsByClassName("leader-line");

        for (let index = 0; index < lineArray2.length; index++) {
          lineArray2[index].style.zIndex = 3000;
        }
        if (this.lineArray && this.lineArray.length > 0) {
          this.lineArray.forEach((a) => {
            a.show();
          });
        }

        if (this.newLineArray && this.newLineArray.length > 0) {
          this.newLineArray.forEach((a) => {
            console.log(a);
            let doc = document.getElementById(a.end.id);

            console.log(doc);
            if (doc == null) {
              a.remove();
            }
            a.show();
          });
        }

        if (this.catalogsLineArray && this.catalogsLineArray.length > 0) {
          this.catalogsLineArray.forEach((a) => {
            a.show();
          });
        }

        // this.registerEvent();
      });
    },
    getArchivesType() {
      archivesTypeApi
          .getDataByTypeName({
            itemCode: this.itemCode,
            sblsh: this.sblsh,
            stage: this.newStageValue,
          })
          .then((a) => {
            this.catalogs = a.data;

            this.$nextTick((a) => {
              this.initCatalogsLine();
              this.setBackColor();
            });
          });
    },
    initCatalogsLine() {
      let start = document.getElementById("div_2_1");
      for (
          let index = 0;
          index < this.$refs.material.children.length;
          index++
      ) {
        let obj = this.$refs.material.children[index];
        this.catalogsLineArray.push(
            new LeaderLine(
                start,
                document.getElementById(obj.id),
                this.styleOption
            )
        );
      }
      // let that = this;
      // this.$nextTick((a) => {
      this.showLine();
      // });
    },
    getReadyFiles() {
      this.fileArray = [];
      this.files2 = [];
      // this.removeLine(1);
      materialsBillApi
          .getReadyFiles({
            itemCode: this.itemCode,
            sblsh: this.sblsh,
            stage: this.newStageValue,
          })
          .then((a) => {
            if (this.newStageValue == "4") {
              //事项办结
              // this.removeDom()
              this.setReadyBackground(a);
            } else {
              this.fileArray = a.data;
              this.setReadyBackground(a);
              this.appendLine();
              // this.showLine();
            }
          });
    },
    setReadyBackground(a) {
      this.removeFileLine();
      if (!a) {
        this.setBackColor();
        document.getElementById("process").style.background = "red";
        document.getElementById("process").style.color = "white";

        document.getElementById("basic").style.background = "red";
        document.getElementById("basic").style.color = "white";

        document.getElementById("file").style.background = "red";
        document.getElementById("file").style.color = "white";

        document.getElementById("result").style.background = "red";
        document.getElementById("result").style.color = "white";

        document.getElementById("div_2_2").style.background = "red";
        document.getElementById("div_2_2").style.color = "white";
      } else {
        this.setBackColor(a);
        a.data.forEach((s) => {
          if (s.name == "办理流程.xml") {
            document.getElementById("process").style.background = "green";
            document.getElementById("process").style.color = "white";
          }
          if (s.name == "基本信息.xml") {
            document.getElementById("basic").style.background = "green";
            document.getElementById("basic").style.color = "white";
          }
          if (s.name == "归档配置信息.xml") {
            document.getElementById("file").style.background = "green";
            document.getElementById("file").style.color = "white";
          }
          if (s.name == "结果信息描述.xml") {
            document.getElementById("result").style.background = "green";
            document.getElementById("result").style.color = "white";
          }
          if (s.name == "归档信息包签名文件.pdf") {
            document.getElementById("div_2_2").style.background = "green";
            document.getElementById("div_2_2").style.color = "white";
          }
        });
      }
    },
    removeDom() {
      let index = 0;
      // debugger
      if (this.newLineArray && this.newLineArray.length > 0) {
        this.newLineArray.forEach((s) => {
          // debugger
          s.remove();
          index++;
        });
      }
      this.newLineArray = [];

      for (let index = 0; index < this.$refs.file.children.length; index++) {
        let obj = this.$refs.file.children[index];
        this.$refs.file.removeChild(obj);
      }
    },
    appendLine() {
      let keyObjs = [];
      this.fileArray.forEach((a) => {
        let keys = tool.getObjectKeys(a);

        let tempIdArray = [];

        for (
            let index = 0;
            index < this.$refs.material.children.length;
            index++
        ) {
          let obj = this.$refs.material.children[index];
          if (keys[0] == obj.innerText) {
            tempIdArray.push(obj.id);

            let values = tool.getObjectValues(a);
            values.forEach((f) => {
              f.forEach((v) => {
                this.files2.push(v);
              });
            });
            keyObjs.push({key: obj.id, files: this.files2});
          }
        }
      });
      this.$nextTick(() => {
        this.initAppendLine(keyObjs);
      });
      // this.showLine();
    },
    showFileLine() {
      this.newLineArray.forEach((s) => {
        s.show();
      });

      let lineArray2 = document.getElementsByClassName("leader-line");

      for (let index = 0; index < lineArray2.length; index++) {
        lineArray2[index].style.zIndex = 3000;
      }
    },
    //删除材料line
    removeFileLine() {
      this.newLineArray.forEach((s) => {
        s.remove();
      });
      this.newLineArray = [];
    },
    /**
     * @param val 父级id
     * @param array 文件中文集合
     * **/
    initAppendLine(array) {
      this.removeFileLine();
      array.forEach((s) => {
        let start = document.getElementById(s.key);
        start.style.background = "green";
        for (let index = 0; index < this.$refs.file.children.length; index++) {
          let obj = this.$refs.file.children[index];
          obj.style.background = "green";
          obj.style.color = "white";
          let fileNum = s.files.filter((f) => f == obj.innerText).length;
          if (fileNum > 0) {
            let lineObj = new LeaderLine(
                start,
                document.getElementById(obj.id),
                this.styleOption
            );
            this.newLineArray.push(lineObj);
          }
        }
      });

      this.showFileLine();
    },
    /**
     * 设置背景颜色
     * @param val id集合
     * **/
    setBackColor(val) {
      for (
          let index = 0;
          index < this.$refs.material.children.length;
          index++
      ) {
        this.$refs.material.children[index].style.background = "red";
        this.$refs.material.children[index].style.color = "white";
      }
      if (val) {
        for (
            let index = 0;
            index < this.$refs.material.children.length;
            index++
        ) {
          let obj = this.$refs.material.children[index];

          if (obj.innerText == val) {
            obj.style.background = "green";
            obj.style.color = "white";
          }
          //排除xml
          if (
              obj.innerText != "基本信息.xml" &&
              obj.innerText != "办理流程.xml" &&
              obj.innerText != "办理流程.xml" &&
              obj.innerText != "归档配置信息.xml" &&
              obj.innerText != "结果信息描述.xml"
          ) {
            obj.style.background = "gray";
            obj.style.color = "white";
          }
        }
      }
    },

    //注册事件
    registerEvent() {
      let that = this;
      this.$nextTick((f) => {
        window.addEventListener(
            "scroll",
            function () {
              if (that.lineArray && that.lineArray.length > 0) {
                that.lineArray.forEach((a) => {
                  let obj = this.document.getElementById(a.end.id);
                  if (obj) {
                    a.positionByWindowResize = false;
                    a.position();
                  }
                });
              }

              if (that.newLineArray && that.newLineArray.length > 0) {
                that.newLineArray.forEach((a) => {
                  let obj = this.document.getElementById(a.end.id);
                  if (obj) {
                    a.positionByWindowResize = false;
                    a.position();
                  }
                });
              }

              if (that.catalogsLineArray && that.catalogsLineArray.length > 0) {
                that.catalogsLineArray.forEach((a) => {
                  a.positionByWindowResize = false;
                  a.position();
                });
              }
            },

            true
        );
      });
    },
    //销毁 0销毁全部 1是销毁newFileArray
    removeLine(val) {
      this.$nextTick((a) => {
        if (val == 0) {
          if (this.lineArray && this.lineArray.length > 0) {
            this.lineArray.forEach((s) => {
              s.remove();
            });
          }
          if (this.catalogsLineArray && this.catalogsLineArray.length > 0) {
            this.catalogsLineArray.forEach((s) => {
              s.remove();
            });
          }
        }
        if (this.newLineArray && this.newLineArray.length > 0) {
          this.newLineArray.forEach((s) => {
            // let obj = document.getElementsByClassName("leader-line")[s._id - 1];
            // if (obj != null) {
            s.remove();
            // } else {
            //   this.newLineArray.splice(0, 1);
            // }
          });
        }
      });
    },
  },
  mounted() {
    this.initLine();
    this.getArchivesType();
    this.getReadyFiles();
    this.setBackColor();

    this.registerEvent();
  },
};
</script>
<style scoped>
.zipDiv {
  position: relative;
  width: 100%;
  height: 60vh;
}

.zipDiv .zipFirst {
  padding: 5px;
  width: 200px;
  border-radius: 5px;
  text-align: center;
}

.zipDiv .zipFirst .child {
  width: 100%;
  padding: 5px;

  border: 1px solid rgb(70, 202, 18);
  margin-bottom: 10px;
  border-radius: 5px;
  text-align: center;
}

#div_2_2 {
  background: red;
  color: #f3f0f0;
}
</style>

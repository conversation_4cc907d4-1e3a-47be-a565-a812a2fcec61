<template>
  <div>
    <el-tabs type="border-card" v-model="activeName" @tab-click="tabClick">
      <el-tab-pane label="基础信息" name="basic">
        <BusinessBasice :form="form" v-if="pass"></BusinessBasice>
      </el-tab-pane>
      <el-tab-pane label="采集信息" name="collection">
        <el-table :data="tableData" style="width: 100%" height="650" border>
          <el-table-column prop="taskCode" label="序号" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="content" label="同步内容" align="center"></el-table-column>
          <el-table-column prop="result" label="结果" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.result == 0" type="success">成功</el-tag>
              <el-tag v-if="scope.row.result == 1" type="danger">失败</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="同步时间" align="center"></el-table-column>
          <el-table-column prop="stage" label="流程" align="center">
            <template slot-scope="scope">
              <!-- 0事项受理 1申办基础数据  2申办基础材料  3事项过程  4事现办结 5材料同步 6文件上传     -->
              <el-tag v-if="scope.row.stage == 0">事项受理</el-tag>
              <el-tag v-if="scope.row.stage == 1">申办基础数据</el-tag>
              <el-tag v-if="scope.row.stage == 2">申办基础材料</el-tag>
              <el-tag v-if="scope.row.stage == 3">事项过程</el-tag>
              <el-tag v-if="scope.row.stage == 4">事现办结</el-tag>
              <el-tag v-if="scope.row.stage == 5">材料同步</el-tag>
              <el-tag v-if="scope.row.stage == 6">文件上传</el-tag>
              <el-tag v-if="scope.row.stage == 9">zip移交开始</el-tag>
              <el-tag v-if="scope.row.stage == 10">zip移交结束</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="taskCode" label="事项编码" align="center"></el-table-column>
          <el-table-column prop="taskName" label="事项名称" align="center"></el-table-column>
          <el-table-column prop="sblsh" label="流水号" align="center"></el-table-column>
          <!-- </el-table-column> -->
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="材料信息" name="materiInfo">
        <el-table :data="matterialTableData" style="width: 100%" height="650">
          <el-table-column prop="taskCode" label="序号" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="fileName" label="材料名称" align="center"></el-table-column>
          <el-table-column prop="createTime" label="同步时间" align="center"></el-table-column>
          <el-table-column prop="type" label="文件类型" align="center"></el-table-column>
          <el-table-column prop="size" label="文件大小(B)" align="center"></el-table-column>
        </el-table>
      </el-tab-pane>
      <!-- <el-tab-pane label="目录信息" name="catagory" style="height: 2050px">
        <el-steps align-center :active="processStatus.active" finish-status="success">
          <el-step title="收件" :status="processStatus.sbjc.result == 1 ? 'success' : 'wait'" :description="
            processStatus.sbjc.count > 0
              ? `收件完成时间:` + processStatus.sbjc.createTime
              : ''
          "></el-step>
          <el-step title="受理" :status="processStatus.itemSl.result == 1 ? 'success' : 'wait'" :description="
            processStatus.itemSl.count > 0
              ? `受理完成时间:` + processStatus.itemSl.createTime
              : ''
          "></el-step>
          <el-step title="审批" :status="processStatus.itemGC.result == 1 ? 'success' : 'wait'" :description="
            processStatus.itemGC.count > 0
              ? `审批完成时间:` + processStatus.itemGC.createTime
              : ''
          "></el-step>
          <el-step title="办结" :status="processStatus.itemBj.result == 1 ? 'success' : 'wait'" :description="
            processStatus.itemBj.count > 0
              ? `办结完成时间:` + processStatus.itemBj.createTime
              : ''
          "></el-step>
          <el-step title="出证" :status="processStatus.itemLz.result == 1 ? 'success' : 'wait'" :description="
            processStatus.itemLz.count > 0
              ? `出证完成时间:` + processStatus.itemLz.createTime
              : ''
          "></el-step>
          <el-step title="采集" :status="processStatus.itemCaiJi.result == 1 ? 'success' : 'wait'" :description="
            processStatus.itemCaiJi.count > 0
              ? `办结完成时间:` + processStatus.itemCaiJi.createTime
              : ''
          "></el-step>
          <el-step title="生成信息包" :status="processStatus.itemZip.result == 1 ? 'success' : 'wait'" :description="
            processStatus.itemZip.count > 0
              ? `生成信息包完成时间:` + processStatus.itemZip.createTime
              : ''
          "></el-step>
           <el-step
            title="送达"
            :status="
              processStatus.itemHandover.result == 1 ? 'success' : 'wait'
            "
            :description="
              processStatus.itemHandover.count > 0
                ? `送达完成时间:` + processStatus.itemHandover.createTime
                : ''
            "
          ></el-step>
        </el-steps>
        <InfreedTree v-if="pass2" :noNum="form.ArchivesNo" :list="treeData" ref="InfreedTree"></InfreedTree>
      </el-tab-pane> -->
      <el-tab-pane label="状态展示" name="statusShow">
        <div class="divStyle">
          <p style="font-size: 16px;font-weight: 500;margin-bottom: 0;margin-top: 5px;">说明</p>
          <p style="font-size: 14px;margin-top: 5px;">红色：归档必要材料<span style="margin-left: 10px;">绿色：必要且生成的材料</span><span
              style="margin-left: 10px;">灰色：空目录</span></p>
        </div>
        <el-steps align-center :active="processStatus.active" finish-status="success">
          <el-step ref="receiving" @click.native="fileStatus('0', processStatus.sbjc.result)" title="收件"
            :status="processStatus.sbjc.result == 1 ? 'success' : 'wait'" :description="
              processStatus.sbjc.count > 0
                ? `收件完成时间:` + processStatus.sbjc.createTime
                : ''
            "></el-step>
          <el-step @click.native="fileStatus('1', processStatus.itemSl.result)" title="受理" ref="acceptance"
            :status="processStatus.itemSl.result == 1 ? 'success' : 'wait'" :description="
              processStatus.itemSl.count > 0
                ? `受理完成时间:` + processStatus.itemSl.createTime
                : ''
            "></el-step>
          <el-step @click.native="fileStatus('2', processStatus.itemGC.result)" title="审批" ref="check"
            :status="processStatus.itemGC.result == 1 ? 'success' : 'wait'" :description="
              processStatus.itemGC.count > 0
                ? `审批完成时间:` + processStatus.itemGC.createTime
                : ''
            "></el-step>
          <el-step @click.native="fileStatus('3', processStatus.itemBj.result)" title="办结" ref="finish"
            :status="processStatus.itemBj.result == 1 ? 'success' : 'wait'" :description="
              processStatus.itemBj.count > 0
                ? `办结完成时间:` + processStatus.itemBj.createTime
                : ''
            "></el-step>
          <el-step @click.native="fileStatus('5', processStatus.itemLz.result)" ref="license" title="出证"
            :status="processStatus.itemLz.result == 1 ? 'success' : 'wait'" :description="
              processStatus.itemLz.count > 0
                ? `出证完成时间:` + processStatus.itemLz.createTime
                : ''
            "></el-step>
          <el-step @click.native="
            fileStatus('0,1,2,3', processStatus.itemCaiJi.result)
          " title="采集" ref="collection" :status="processStatus.itemCaiJi.result == 1 ? 'success' : 'wait'"
            :description="
              processStatus.itemCaiJi.count > 0
                ? `采集完成时间:` + processStatus.itemCaiJi.createTime
                : ''
            "></el-step>
          <el-step @click.native="fileStatus('4', processStatus.itemZip.result)" title="生成信息包" ref="gen"
            :status="processStatus.itemZip.result == 1 ? 'success' : 'wait'" :description="
              processStatus.itemZip.count > 0
                ? `生成信息包完成时间:` + processStatus.itemZip.createTime
                : ''
            "></el-step>
          <!-- <el-step
            title="送达"
            
            :status="
              processStatus.itemHandover.result == 1 ? 'success' : 'wait'
            "
            :description="
              processStatus.itemHandover.count > 0
                ? `送达完成时间:` + processStatus.itemHandover.createTime
                : ''
            "
          ></el-step> -->
        </el-steps>
        <ZipStatus v-if="pass3" :form="form" :stage="String(stage)" :files="fileData" :sblsh="sblsh"
          :itemCode="form.taskcode"></ZipStatus>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<style scoped>
.el-step__head:hover {
  color: #67c23a;
  border-color: #67c23a;
}
</style>
<script>
import ZipStatus from "./ZipStatus.vue";
import BusinessBasice from "./BusinessBasice.vue";
import InfreedTree from "./InFeedTreeCommon.vue";
import { itemBusinessApi } from "../api/itemBusiness";
import { materialsBillApi } from "../api/materialsBill";
import { logApi } from "../api/log";

export default {
  props: {
    sblsh: {
      type: String,
      default: () => "",
    },
    // form:{
    //     type:Object,
    //     default:()=>{}
    // },
    // matterialTableData:{
    //     type:Array,
    //     default:()=>[]
    // },
    // tableData:{
    //     type:Array,
    //     default:()=>[]
    // },
    // treeData:{
    //     type:Array,
    //     default:()=>[]
    // }
  },
  data() {
    return {
      stage: 0,
      fileData: [],
      activeName: "basic",
      form: {},
      matterialTableData: [],
      tableData: [],
      logQuery: {
        pageNo: 0,
        pageSize: 100,
        sblsh: this.sblsh,
      },
      processStatus: {},
      pass: false,
      pass2: false,
      pass3: false,
      treeData: [],
      showfields: [
        {
          name: "服务名称",
          key: "name",
        },
        {
          name: "服务编号",
          key: "name",
        },
      ],
    };
  },
  created() {
    this.getData();
  },
  components: {
    ZipStatus,
    BusinessBasice,
    InfreedTree,
  },
  methods: {
    initStep() {
      let divValue =
        '<i class="el-step__icon-inner is-status el-icon-check"></i>';
      if (
        this.$refs.receiving.$el.getElementsByClassName("is-success")[0] != null
      ) {
        this.$refs.receiving.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      }

      if (
        this.$refs.acceptance.$el.getElementsByClassName("is-success")[0] !=
        null
      ) {
        this.$refs.acceptance.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      }

      if (
        this.$refs.check.$el.getElementsByClassName("is-success")[0] != null
      ) {
        this.$refs.check.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      }

      if (
        this.$refs.finish.$el.getElementsByClassName("is-success")[0] != null
      ) {
        this.$refs.finish.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      }

      if (
        this.$refs.license.$el.getElementsByClassName("is-success")[0] != null
      ) {
        this.$refs.license.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      }

      if (
        this.$refs.collection.$el.getElementsByClassName("is-success")[0] !=
        null
      ) {
        this.$refs.collection.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      }

      if (this.$refs.gen.$el.getElementsByClassName("is-success")[0] != null) {
        this.$refs.gen.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      }
    },

    fileStatus(val, result) {
      let divValue =
        '<div style="margin:0px auto;width:30px;border:2px solid"><div class="el-step__icon is-text"><i class="el-step__icon-inner is-status el-icon-check"></i></div></div>';
      this.initStep();

      if (val == "0" && result == 1) {
        this.$refs.receiving.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      } else if (val == "1" && result == 1) {
        this.$refs.acceptance.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      } else if (val == "2" && result == 1) {
        this.$refs.check.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      } else if (val == "3" && result == 1) {
        this.$refs.finish.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      } else if (val == "5" && result == 1) {
        this.$refs.license.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      } else if (val == "0,1,2,3" && result == 1) {
        this.$refs.collection.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      } else if (val == "4" && result == 1) {
        this.$refs.gen.$el.getElementsByClassName(
          "el-step__icon"
        )[0].innerHTML = divValue;
      }
      if (result == 1) {
        this.stage = val;
      }
    },
    tabClick(tab, event) {
      if (tab.name == "catagory") {
        this.pass2 = true;
        this.pass3 = false;
      } else if (tab.name == "statusShow") {
        this.pass3 = true;
        this.pass2 = false;
      } else {
        this.pass3 = false;
        this.pass2 = false;
      }
    },
    getData() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      Promise.all([
        new Promise((re, rp) => {
          itemBusinessApi.getItemSLEntity({ sblsh: this.sblsh, name: 'basic' }).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          logApi.page2(this.logQuery).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          itemBusinessApi.getFileBySblshAllNoPage(this.logQuery).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          materialsBillApi
            .getMaterialBillTree({ sblsh: this.sblsh })
            .then((a) => {
              re(a);
            });
        }),
        new Promise((re, rp) => {
          itemBusinessApi.getProgressStatus({ sblsh: this.sblsh }).then((a) => {
            re(a);
          });
        }),
      ])
        .then((a) => {
          this.form = a[0].data;
          this.tableData = a[1].data.content;
          this.matterialTableData = a[2].data.filter(
            (s) => s.state || s.state != 4
          );
          this.fileData = a[2].data;
          this.treeData = a[3].data;
          this.processStatus = a[4].data;
          this.pass = true;
          loading.close();
        })
        .catch((s) => {
          loading.close();
        });
    },
  },
};
</script>
<style scoped>
.divStyle {
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
}
</style>

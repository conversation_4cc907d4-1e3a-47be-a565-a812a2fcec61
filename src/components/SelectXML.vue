<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="basic"
        ><XMLTree :taskCode="this.taskCode" type="basic" @ids="getIds"
      /></el-tab-pane>
       <!-- <el-tab-pane label="办理流程信息" name="gc"
        ><XMLTree :taskCode="this.taskCode" type="gc" @ids="getIds"
      /></el-tab-pane> -->
      <el-tab-pane label="归档配置信息" name="pz">
        <XMLTree :data="data" :taskCode="this.taskCode" type="pz" @ids="getIds" />
      </el-tab-pane>
      
     
    </el-tabs>
    
  </div>
</template>
<script>
import XMLTree from "./XMLTree.vue";
import { getMatedateAll, deleteMatedate } from "../api/first";
export default {
  props: {
    taskCode: String,
  },
  data() {
    return {
      activeName: "basic",
      ids: [],
      data:[]
      
    };
  },
  created() {
    this.$store.commit("getName", this.activeName);
    this.getData({itemsCode: localStorage.getItem("taskCode"),name:this.activeName})
  },
  mounted() {
    // this.getIds()
  },
  methods: {
    handleClick(val) {
      console.log(val)
      this.activeName = val.name
      this.$store.commit("getName", this.activeName);
      let obj = {itemsCode: localStorage.getItem("taskCode"),name:val.name}
      // f(tad.name == 'basic'){
      //   obj.namme = 
      // }i
      this.getData(obj)
    },
    getIds(ids) {
      this.ids = ids;
      this.$parent.$parent.getIds(this.ids);
      console.log("SelectXMLIds======" + this.ids);
      this.$emit('transfer',this.activeName)
    },
    getData(obj){
      getMatedateAll(obj).then(
        (res) => {
          console.log(res);
          this.data = res.data;
          this.$store.commit("getData", res.data);
        })
    }
  },
  components: { XMLTree },
};
</script>

<style lang="scss" scoped></style>

<template>
    <div class="box">
        <div class="searchDiv">
            <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
                <el-form-item label="目录名称">
                    <el-input v-model="query.name" placeholder="目录名称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleAdd">添加</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table :data="tableData" style="width: 100%" :height="tableHeight"
            :header-cell-style="{ background: '#F6F7FA' }">
            <el-table-column label="序号" align="center">
                <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                </template>
            </el-table-column>
            <el-table-column prop="name" label="目录名" align="center"></el-table-column>
            <el-table-column prop="sequence" label="排序" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-edit"
                        @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                    <el-button type="text" icon="el-icon-delete"
                        @click="handleRemove(scope.$index, scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page.sync="query.pageNo" :page-size="query.pageSize" layout="total, prev, pager, next"
            :total="query.total">

        </el-pagination> -->
        <el-pagination v-model:page-size="query.pageSize"
            :page-sizes="[10, 20, 50, 100]"  :disabled="disabled" 
            layout="total, sizes, prev, pager, next, jumper" :total="query.total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" style="float: right;margin-top: 10px;" />
        <el-dialog :title="dialog.title" width="30%" :visible.sync="dialog.dialogVisible" :before-close="handleClose"
            center :append-to-body="true" :close-on-click-modal="false">
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" size="small" label-width="100px"
                class="demo-ruleForm">
                <el-form-item label="目录名称" prop="name">
                    <el-input v-model="ruleForm.name" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="序号" prop="sequence">
                    <el-input v-model="ruleForm.sequence"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { zipPackageApi } from '../api/zipPackage'
export default {
    props: {
        archivesType: String
    },
    data() {
        return {
            tableHeight: 650,
            query: {
                archivesTypeId: this.archivesType,
                pageNo: 0,
                pageSize: 10,
                total: 0
            },
            dialog: {
                title: '新增',
                dialogVisible: false
            },
            rules: {
                sequence: [
                    { required: true, message: '请输入排序号', trigger: 'blur' },
                    { validator: this.checkPrice, trigger: 'change' }
                ],
              name: [
                {required: true, message: '请输入目录名称', trigger: 'blur'}
              ]
            },
          ruleForm: {},
          tableData: []
        }
    },
  created() {
    this.getData();
  },
  mounted() {
    this.initTableHeight();
  },
  methods: {
    //验证数字
    checkPrice(rule, value, callback) {
      if (value) {
        let rgx = /^-?[1-9]\d*$/;
        if (value.toString().match(rgx) == null) {
          return callback(new Error('请检查输入格式，必须为整数'))
        } else {
                    callback();
                }
            }
        },
        initTableHeight() {
            //获取窗口高度
            let height = document.documentElement.clientHeight;
            let that = this;
            // 一定要使用 nextTick 来改变height 不然不会起作用
            //nav 45;
            //padding:10
            this.$nextTick(() => {
                let tempHeight = height - 54 - 25 - 51 - 70 - 100;

                this.tableHeight = tempHeight
                console.log(this.tableHeight, "height");
            });
        },
        handleAdd() {
            this.ruleForm = {};
            this.ruleForm.archivesTypeId = this.query.archivesTypeId;
            this.dialog.title = "新增";
            this.dialog.dialogVisible = true;
        },
        search() {
            this.query.pageNo = 0;
            this.getData();
        },
        handleEdit(index, row) {
            this.dialog.title = "编辑";
            this.ruleForm = JSON.parse(JSON.stringify(row));
            this.ruleForm.archivesTypeId = this.query.archivesTypeId;
            this.dialog.dialogVisible = true;
        },
        handleClose(done) {
            this.form = {};
            done();
        },
        handleRemove(index, row) {
            this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                zipPackageApi.remove({ id: row.id }).then(a => {
                    if (a.code == 200) {
                        this.$message.success("删除成功");
                        this.query.pageNo = 0;
                        this.getData();
                    } else {
                        this.$message.error("删除失败");
                    }
                });
            }).catch(() => {

            });
        },
        cancel() {
            this.form = {};
            this.dialog.dialogVisible = false;
        },
        save(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    zipPackageApi.save(this.ruleForm).then(a => {
                        if (a.code == 200) {
                            this.$message.success("保存成功");
                            this.query.pageNo = 0;
                            this.cancel();
                            this.getData();
                        } else {
                            this.$message.error("保存失败");
                        }
                    })
                } else {
                    console.log('error submit!!');
                    return false;
                }
            })
        },
        getData() {
            this.query.archivesTypeId = this.archivesType;
            zipPackageApi.page(this.query).then(a => {
                if (a.code == 200) {
                    this.tableData = a.data.content;
                    this.query.total = a.data.totalElements;
                }
            })
        },
        handleSizeChange(val) {
            this.query.pageNo = 0;
          this.query.pageSize = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.pageNo = val - 1;
            this.getData();
        },
    }
}
</script>
<style scoped>
.el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /*height:600px;*/
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
}

.el-dialog .el-dialog__body {
    flex: 1;
    overflow: auto;
}

.box {
    padding: 10px
}

.searchDiv {
    margin-top: 10px;
}
</style>
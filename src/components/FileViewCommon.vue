<template>
  <div>
    <el-row :gutter="20">
      <!-- 菜单导航 -->
      <el-col :span="5">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane label="材料目录" name="first">
            <el-table
              height="500"
              border
              :data="tableData"
              style="width: 100%"
              @row-click="handleRow"
            >
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                label="内容"
                align="center"
              >
              </el-table-column>
<!--              <el-table-column prop="range" label="页号范围" align="center">
              </el-table-column>-->
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="基本信息" name="second">
            <div class="basicTxt">档案号: {{ itemSl.archivesNo }}</div>
            <div class="basicTxt">生成zip时间: {{ itemSl.archivesTime }}</div>
            <div class="basicTxt">流水号: {{ itemSl.sblsh }}</div>
            <div class="basicTxt">事项编码: {{ itemSl.taskCode }}</div>
            <div class="basicTxt">事项名称: {{ itemSl.taskName }}</div>
            <div class="basicTxt">部门名称: {{ itemSl.orgName }}</div>
            <div class="basicTxt">部门编码: {{ itemSl.orgCode }}</div>
            <div class="basicTxt">联系人: {{ itemSl.contactName }}</div>
            <div class="basicTxt">联系电话: {{ itemSl.contactTel }}</div>
            <div class="basicTxt">处理人: {{ itemSl.handleUserName }}</div>
            <div class="basicTxt">申请人名称: {{ itemSl.applyerName }}</div>
            <div class="basicTxt">
              申请人证件号码: {{ itemSl.applyerPageCode }}
            </div>
            <div class="basicTxt">事项类型: {{ itemSl.taskTypeName }}</div>
            <div class="basicTxt">事项版本: {{ itemSl.taskVersion }}</div>
            <div class="basicTxt" style="border-bottom: 1px solid black">
              办件类型: {{ itemSl.projectTypeName }}
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="19" style="position: relative">
        <div
          style="
            font-size: 30px;
            margin-left: 40px;
            margin-top: 45px;
            position: absolute;
            text-align: center;
            z-index: 999;
          "
        >
<!--          {{ startNum }}-->
        </div>
        <div class="previewModal" id="previewModal">
          <div class="identifyImgPage" id="father" style="overflow-y: auto;">
            <img
              :style="isClickBig == true ? rotateFun : scaleFun"
              v-show="
                fileType == 'png' || fileType == 'jpeg' || fileType == 'jpg'
              "
              :src="imgUrl"
              :width="imgShow ? '100%' : 'auto'"
              :height="imgShow ? 'auto' : '100%'"
              v-drag
            />
            <!-- <img
            :style="isClickBig==true?rotateFun:scaleFun"
              v-show="isNotFound==true"
              :src="imgUrl"
              :width="imgShow ? '100%' : 'auto'"
              :height="imgShow ? 'auto' : '100%'"
              v-drag
            /> -->
            <pdf
              v-drag
              :src="imgUrl"
              v-show="fileType == 'pdf' && isNotFound != true"
              ref="pdf"
              :style="isClickBig == true ? rotateFun : scaleFun"
            >
            </pdf>
          </div>
        </div>
        <div class="tool">
          <ul>
            <li class="allLast" @click="allLast">
              <i class="el-icon-d-arrow-left"></i>
            </li>
            <li class="last" @click="last">
              <i class="el-icon-arrow-left"></i>
            </li>
            <li>
              <el-input
                disabled
                v-model="startNum"
                size="mini"
                style="width: 50px"
                placeholder="请输入内容"
              ></el-input>
            </li>
            <li style="margin-left: 5px; margin-right: 5px">/</li>
            <li>
              <el-input
                v-model="endNum"
                size="mini"
                disabled
                style="width: 50px; margin-right: 10px"
                placeholder="请输入内容"
              ></el-input>
            </li>
            <li class="next" @click="next">
              <i class="el-icon-arrow-right"></i>
            </li>
            <li class="allNext" @click="allNext">
              <i class="el-icon-d-arrow-right"></i>
            </li>
            <li class="rotate" @click="rotate">
              <i class="el-icon-refresh"></i>
            </li>
            <li class="rotate" @click="enlarge">
              <i class="el-icon-plus"></i>
            </li>
            <li class="rotate" @click="narrow">
              <i class="el-icon-minus"></i>
            </li>
          </ul>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { itemBusinessApi } from "../api/itemBusiness";
// import pdf from "vue-pdf-signature";
import pdf from "vue-pdf";
import CMapReaderFactory from "vue-pdf/src/CMapReaderFactory.js";
// import CMapReaderFactory from 'vue-pdf-signature/src/CMapReaderFactory'
import axios from "axios";
import $ from "jquery";
export default {
  components: {
    pdf,
  },
  props: {
    sblsh: {
      type: String,
    },
  },
  computed: {
    scaleFun: function () {
      var multiples = this.multiples;
      return `transform:scale(${multiples})`;
    },
    rotateFun: function () {
      var angle = this.angle;
      return `transform:rotate(${angle}deg)`;
    },
  },
  data() {
    return {
      multiples: 1,
      isClickBig: true,
      notFoundUrl: require("../assets/img/404.jpg"),
      isNotFound: false,
      rotates: false,
      //旋转角度
      angle: 0,
      activeName: "first",
      fileType: "",
      itemSl: {},

      currentWidth: 0,
      currentHeight: 0,
      //目录下表
      catalogueIndex: 0,
      current: 0,
      //图片
      picture: [],
      //缩略图
      thumbnail: [],
      //目录
      catalogue: [],
      scale: 100,
      startNum: 1,
      endNum: 0,
      show: false, // 弹窗是否显示
      imgShow: true,
      imgUrl: "",
      tableData: [],
    };
  },
  directives: {
    //注册指令

    drag: function (el, binding) {
      let oDiv = el; //当前元素

      oDiv.onmousedown = function (e) {
        e.preventDefault();
        let bw = document.body.clientWidth;

        let bh = document.body.clientHeight;

        //鼠标按下，计算当前元素距离可视区的距离

        let disX = e.clientX - oDiv.offsetLeft;

        let disY = e.clientY - oDiv.offsetTop;

        // 计算两边坐标

        document.onmousemove = function (e) {
          let l = 0,
            t = 0;

          // 拖动边界

          if (e.clientX >= bw) {
            l = bw - disX;
          } else if (e.clientX <= 0) {
            l = 0 - disX;
          } else {
            l = e.clientX - disX;
          }

          if (e.clientY >= bh) {
            t = bh - disY;
          } else if (e.clientY <= 0) {
            t = 0 - disY;
          } else {
            t = e.clientY - disY;
          }

          //移动当前元素

          oDiv.style.left = l + "px";

          oDiv.style.top = t + "px";
        };

        // 鼠标停止移动时，事件移除

        document.onmouseup = function (e) {
          document.onmousemove = null;

          document.onmouseup = null;
        };
      };

      // $(oDiv).bind("mousewheel", function (e) {
      //   // 获取鼠标所在位置
      //   let { clientX, clientY } = e;

      //   // 获取元素距离屏幕边界左边和上边距离
      //   let offsetX = $(oDiv).offset().left;
      //   let offsetY = $(oDiv).offset().top;

      //   // 获取鼠标距离当前元素边界左边和上边距离
      //   let mouseToBorderX = clientX - offsetX;
      //   let mouseToBorderY = clientY - offsetY;

      //   // 获取元素width,height,left,top;注意元素为relative或absolute定位
      //   let width = $(oDiv).width();
      //   let height = $(oDiv).height();
      //   let left = parseFloat($(oDiv).css("left"));
      //   let top = parseFloat($(oDiv).css("top"));

      //   // 设置一下缩放幅度 ,值越大缩放的越快
      //   let ratio = e.originalEvent.deltaY < 0 ? 0.1 : -0.1;

      //   // 设置缩放后的宽高
      //   width = width * (1 + ratio);
      //   height = height * (1 + ratio);

      //   // 这里是关键一步

      //   // 可以想象,当元素宽度增加0.1倍,如果此时元素left值不变化，那么元素是会向右变大的，那么鼠标相

      //   // 对元素左上角的距离与最开始的距离相比就变了，变化量为mouseToBorderX*ratio，那么让元素left

      //   //  减去变化量即可保证鼠标相对元素不动
      //   left = left - mouseToBorderX * ratio;
      //   top = top - mouseToBorderY * ratio;

      //   if (width > 100) {
      //     $(oDiv).css({
      //       width: width + "px",

      //       height: height + "px",

      //       left: left + "px",

      //       top: top + "px",
      //     });
      //   }
      // });
    },
  },
  methods: {
    //放大
    enlarge() {
      this.isClickBig = false;
      this.multiples += 0.1;
    },
    //缩小
    narrow() {
      this.isClickBig = false;
      this.multiples -= 0.1;
    },
    handleClick(event) {},
    getFile(val) {
      let tempUr = "";
      if (val) {
        this.fileType = val.split(".")[1];
        if (process.env.VUE_APP_FILE_API == undefined) {
          tempUr = "http://************:8088/api/file" + val;
        } else {
          tempUr = process.env.VUE_APP_FILE_API + val;
        }
      }
      return tempUr;
    },
    handleThumbnailClick(item, index) {
      this.startNum = index + 1;
      this.imgUrl = item;
    },
    handleRow(row, column, event) {
      debugger;
      console.log(row);
      this.startNum = 0;
      this.init();
      for (let index = 0; index < this.tableData.length; index++) {
        if (this.tableData[index].name == row.name) {
          this.catalogueIndex = index;
          this.imgUrl = this.exitFileAndSet(
            this.getFile(this.tableData[index].resources[0])
          );
          // console.log(this.tableData[index].resources[0]);
        }
      }

      for (let index = 0; index < this.catalogueIndex; index++) {
        this.startNum += this.tableData[index].resources.length;
      }

      this.startNum += 1;
    },
    //第一页
    allLast() {
      this.init();
      this.startNum = 1;
      let imgUrl = this.tableData[0].resources[this.startNum - 1];
      this.imgUrl = this.exitFileAndSet(this.getFile(imgUrl));
    },
    init() {
      this.angle = 0;
      this.multiples = 1;
    },
    //上一页
    last() {
      this.init();
      if (this.startNum - 1 < 1) {
        return;
      } else {
        this.startNum--;
      }
      this.multiples = 1;
      let tempIndex = this.getCatalogueIndex(this.startNum);
      let tempResourceIndex = this.getResourceIndex(this.startNum);
      let imgUrl = this.tableData[tempIndex].resources[tempResourceIndex];

      this.imgUrl = this.exitFileAndSet(this.getFile(imgUrl));
    },
    getCatalogueIndex(val) {
      let temp = 0;
      let tempCurrent = 0;
      for (let index = 0; index < this.tableData.length; index++) {
        for (
          let index2 = 0;
          index2 < this.tableData[index].resources.length;
          index2++
        ) {
          tempCurrent += 1;
          if (tempCurrent == val) {
            temp = index;
          }
        }
      }
      return temp;
    },
    getResourceIndex(val) {
      let temp = 0;
      let tempCurrent = 0;
      for (let index = 0; index < this.tableData.length; index++) {
        for (
          let index2 = 0;
          index2 < this.tableData[index].resources.length;
          index2++
        ) {
          tempCurrent += 1;
          if (tempCurrent == val) {
            temp = index2;
          }
        }
      }
      return temp;
    },
    //最后一页
    allNext() {
      this.init();
      this.startNum = this.endNum;
      let resource = this.tableData[this.tableData.length - 1].resources;
      let url =
        this.tableData[this.tableData.length - 1].resources[
          resource.length - 1
        ];
      this.multiples = 1;
      this.imgUrl = this.exitFileAndSet(this.getFile(url));
    },
    //下一页
    next() {
      this.init();
      if (this.startNum + 1 > this.endNum) {
        return;
      } else {
        this.startNum++;
      }
      this.multiples = 1;

      let tempIndex = this.getCatalogueIndex(this.startNum);
      let tempResourceIndex = this.getResourceIndex(this.startNum);
      let imgUrl = this.tableData[tempIndex].resources[tempResourceIndex];
      this.imgUrl = this.exitFileAndSet(this.getFile(imgUrl));
    },
    //禁用鼠标滚动 防止页面滚动
    disabledMouseWheel() {
      var div = document.getElementById("previewModal");

      if (div.addEventListener) {
        div.addEventListener("DOMMouseScroll", this.scrollFunc, false);
      }

      div.onmousewheel = div.onmousewheel = this.scrollFunc;
    },
    initData() {
      let temp = 0;
      let randeIndex = 1;
      if (this.tableData.length <= 0) {
        return;
      }
      this.tableData.forEach((a) => {
        temp += a.resources.length;
        a.range = randeIndex + "~" + (randeIndex + a.resources.length - 1);
        randeIndex = randeIndex + a.resources.length;
        a.resources.forEach((f) => {
          let url = this.getFile(f);
          this.thumbnail.push(url);
        });
      });

      this.endNum = temp;

      this.exitFileAndSet(this.getFile(this.tableData[0].resources[0]));
    },
    exitFileAndSet(val) {
      axios
        .get(val)
        .then((a) => {
          if (a.status != 200) {
            this.tempUr = this.notFoundUrl;
            this.isNotFound = true;
          }
          let fileType = val.split(".")[1];
          if (fileType == "pdf") {
            // CMapReaderFactory
            this.imgUrl = pdf.createLoadingTask({
              url: val,
              cMapUrl: "../../public/cmaps/",
              cMapPacked: true,
            });
          } else {
            this.imgUrl = val;
          }
        })
        .catch((e) => {
          this.imgUrl = this.notFoundUrl;
          this.isNotFound = true;
        });
    },
    //旋转
    rotate() {
      this.isClickBig = true;
      this.angle += 90;
    },
    scrollFunc(evt) {
      if (evt.deltaY < 100) {
        this.scale += 5;
      } else {
        this.scale += -5;
      }

      this.$refs.pdf.$el.style.width = parseInt(this.scale) + "%";
      evt = evt || window.event;

      if (evt.preventDefault) {
        // Firefox

        evt.preventDefault();

        evt.stopPropagation();
      } else {
        // IE

        evt.cancelBubble = true;

        evt.returnValue = false;
      }

      return false;
    },
  },
  mounted() {
    // this.disabledMouseWheel();
  },
  created() {
    itemBusinessApi.getReadyFileInfo({ sblsh: this.sblsh }).then((a) => {
      this.tableData = a.data.catalogue;
      this.itemSl = a.data;
      this.initData();
      // this.getFile();
    });
  },
};
</script>
<style scoped>
.sidebar {
  width: 100%;
  border: 1px solid rgb(188, 221, 243);
}
.sidebar .title {
  text-indent: 10px;
  height: 30px;
  line-height: 30px;
  width: 100%;
  background-color: rgb(188, 221, 243);
  margin-bottom: 5px;
}
.sidebar .content {
  width: 100%;
  border: 1px solid rgb(188, 221, 243);
}
.sidebar .content .contentTitle {
  text-indent: 4px;
  height: 30px;
  line-height: 30px;
  width: 100%;
  background-color: rgb(188, 221, 243);
}
.aa {
  transition: all 0.5s;
}

.go {
  transform: rotate(90deg);
  transition: all 0.5s;
}
.el-dialog--center .el-dialog__body {
  text-align: initial;
  padding: 0px 25px 30px;
}
.basicTxt {
  font-size: 14px;
  border-left: 1px solid black;
  border-right: 1px solid black;

  border-top: 1px solid black;
  padding: 10px;
}
.sidebar .content .info {
  width: 100%;
}
.sidebar .content .info .col {
  width: 100%;
  margin-bottom: 5px;
}
.sidebar .content .info .view {
  width: 100%;
  height: 330px;
  overflow: hidden;
  overflow-y: scroll;
}
.sidebar .content .info .titleInfo {
  padding: 5px;
}
.sidebar .content .info .view .viewDiv {
  width: 93%;
  height: 160px;
  margin-bottom: 10px;
  padding: 6px;
}
.sidebar .content .info .view .viewDiv:hover {
  cursor: pointer;
}
.sidebar .content .info .view .viewDiv img {
  width: 100%;
  height: 100%;
}
.sidebar .content .info .view .viewDiv img:hover {
  box-shadow: 2px 2px 10px black;
}
.tool {
  width: 100%;
  height: 30px;
}
.tool ul {
  list-style: none;
}
.tool ul .allLast,
.last,
.next,
.rotate,
.allNext {
  padding: 5px 10px;
  margin-right: 10px;
  border-radius: 4px;
}
.tool ul .allLast:hover {
  cursor: pointer;
  color: white;
  background: rgb(139, 207, 238);
}
.tool ul .rotate:hover {
  cursor: pointer;
  color: white;
  background: rgb(139, 207, 238);
}
.tool ul .last:hover {
  color: white;
  cursor: pointer;
  background: rgb(139, 207, 238);
}
.tool ul .allNext:hover {
  cursor: pointer;
  color: white;
  background: rgb(139, 207, 238);
}
.tool ul .next:hover {
  color: white;
  cursor: pointer;
  background: rgb(139, 207, 238);
}
.tool ul li {
  display: inline;
  line-height: 30px;
}
.previewModal .identifyImgPage {
  position: relative;

  width: 100%;

  height: 83vh;

  z-index: 2;
  border: 1px solid grey;

  overflow: hidden;
}

.previewModal .identifyImgPage img {
  position: absolute;
}
</style>

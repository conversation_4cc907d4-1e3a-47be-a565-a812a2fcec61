<template>
  <div>
    <el-row :gutter="20">
      <el-form ref="form" :model="form" label-width="200px">
        <el-col :span="8">
          <el-form-item label="办件编号">
            <el-input
              v-model="form.projectno"
              placeholder="请输入办件编号"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申办流水号">
            <el-input
              v-model="form.sblsh"
              placeholder="请输入申办流水号"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取人姓名">
            <el-input
              v-model="form.ReceiverName"
              placeholder="请输入领取人姓名"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取人证件类型">
            <el-input
              v-model="form.ReceiverPageType"
              placeholder="请输入领取人证件类型"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取人身份证件号码">
            <el-input
              v-model="form.ReceiverPageNo"
              placeholder="请输入领取人身份证件号码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取方式">
            <el-input
              v-model="form.ReceiveType"
              placeholder="请输入领取方式"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取时间">
            <el-input
              v-model="form.ReceiveTime"
              placeholder="请输入领取时间"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取登记部门所在地行政区划代码">
            <el-input
              v-model="form.ReceiveRegionCode"
              placeholder="请输入领取登记部门所在地行政区划代码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取登记部门统一社会信用代码">
            <el-input
              v-model="form.ReceiveOrgCode"
              placeholder="请输入领取登记部门统一社会信用代码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取登记部门名称">
            <el-input
              v-model="form.ReceiveOrgName"
              placeholder="请输入领取登记部门名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注">
            <el-input v-model="form.Remark" placeholder="请输入备注" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="投资项目统一编码">
            <el-input
              v-model="form.projectco"
              placeholder="请输入投资项目统一编码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据提供部门行政区划代码">
            <el-input
              v-model="form.ProdatAreginCode"
              placeholder="请输入数据提供部门行政区划代码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据提供部门统一社会信用代码">
            <el-input
              v-model="form.CrodataorgCode"
              placeholder="请输入数据提供部门统一社会信用代码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据提供部门名称">
            <el-input
              v-model="form.proDataOrgName"
              placeholder="请输入数据提供部门名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="第三方数据来源名称">
            <el-input
              v-model="form.sourceName"
              placeholder="请输入第三方数据来源名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项部门名称">
            <el-input
              v-model="form.sxbmmc"
              placeholder="请输入事项部门名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务发生事项部门名称">
            <el-input
              v-model="form.ywfsSxbmmc"
              placeholder="请输入业务发生事项部门名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="镇街名称">
            <el-input
              v-model="form.townName"
              placeholder="请输入镇街名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发证人名称">
            <el-input
              v-model="form.winUserName"
              placeholder="请输入发证人名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否为镇街事项">
            <el-input
              v-model="form.hasTown"
              placeholder="请输入是否为镇街事项"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取登记部门所在地行政区划代码">
            <el-input
              v-model="form.RegionCode"
              placeholder="请输入领取登记部门所在地行政区划代码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取登记部门组织机构代码">
            <el-input
              v-model="form.OrgCode"
              placeholder="请输入领取登记部门组织机构代码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领取登记部门名称">
            <el-input
              v-model="form.OrgName"
              placeholder="请输入领取登记部门名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务办理项编码">
            <el-input
              v-model="form.TaskHandleItem"
              placeholder="请输入业务办理项编码"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="接收证书名称">
            <el-input
              v-model="form.ReceiveCertiNames"
              placeholder="请输入接收证书名称"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => {},
    },
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="scss" scoped></style>

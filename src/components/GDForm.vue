<template>
  <div>
    <el-row :gutter="20">
      <el-form ref="form" :model="form" label-width="200px">
        <el-col :span="8">
          <el-form-item label="事项名称">
            <el-input
                v-model="form.taskName"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项类型">
            <el-input
                v-model="form.taskTypeText"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项编码">
            <el-input
                v-model="form.taskCode"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项版本">
            <el-input
                v-model="form.taskVersion"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门名称">
            <el-input
                v-model="form.deptName"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门代码">
            <el-input
                v-model="form.deptCode"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门编码">
            <el-input
                v-model="form.tongYiCode"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="地方实施编码">
            <el-input
                v-model="form.taskCode"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保管期限">
            <el-input
                v-model="form.retentionPeriod.name"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="密级">
            <el-input
                v-model="form.className"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="全宗号">
            <el-input
                v-model="form.qzh"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年度">
            <el-input
                v-model="form.year"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类别号">
            <el-input
                v-model="form.categoryNumber"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="案卷号">
            <el-input
                v-model="form.archivesNo"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="题名">
            <el-input
                v-model="form.tiMing"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="文号">
            <el-input
                v-model="form.wenHao"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="容缺归档标记">
            <el-input
                value="否"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="容缺归档原因">
            <el-input
                value="无"
                readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <div v-if="this.form.Custom">
          <el-col :span="24">
            <p class="text">自定义</p>
          </el-col>
          <div v-for="(item, index) in form.Custom" :key="index">
            <el-col :span="8">
              <el-form-item :label="item.note">
                <el-input v-model="item.settingValues"></el-input>
              </el-form-item>
            </el-col>
          </div>
        </div>
      </el-form>

    </el-row>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => {
      },
    },
  },

  mounted() {
  },

  methods: {},
};
</script>

<style scoped>
.text {
  font-size: 18px;
  margin-left: 60px;
  color: red;
}</style>

<!-- 业务基础数据 -->
<template>
  <div>
    <el-row :gutter="20">
      <el-form ref="form" :model="form" label-width="auto" :disabled="true">
        <el-col :span="24">
          <el-form-item label="电子档案号">
            <el-input v-model="form.archivesNo"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="事项名称">
            <el-input v-model="form.taskname"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项类型">
            <el-input v-model="form.tasktypetext"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项编码">
            <el-input v-model="form.taskcode"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="事项版本">
            <el-input v-model="form.taskversion"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门名称">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门代码">
            <el-input v-model="form.bmbm"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="归档日期">
            <el-input v-model="form.systemtime"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实施编码">
            <el-input v-model="form.implcode"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保管期限">
            <el-input v-model="form.retenname"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="密级">
            <el-input v-model="form.classname"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="全宗号">
            <el-input v-model="form.qzh"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年度">
            <el-input v-model="form.year"></el-input>
          </el-form-item>
        </el-col>
<!--        <el-col :span="8">
          <el-form-item label="类别号">
            <el-input v-model="form.classtype"></el-input>
          </el-form-item>
        </el-col>-->
        <el-col :span="8">
          <el-form-item label="行政相对人名称">
            <el-input v-model="form.bsname"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行政相对人手机">
            <el-input v-model="form.bsphone"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行政相对人证件">
            <el-input v-model="form.bsnum"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法定代表人">
            <el-input v-model="form.legal"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经办人手机号码">
            <el-input v-model="form.jPhone"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人/代理人姓名">
            <el-input v-model="form.agentname"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="证件类型">
            <el-input v-model="form.applyerpagetype"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人/代理人号码">
            <el-input v-model="form.agentphone"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人/代理人证件号码">
            <el-input v-model="form.agentNum"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人/代理人手机号">
            <el-input v-model="form.contactmobile"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
                    <el-form-item label="办件名称">
                        <el-input v-model="form.Prodataorgname"></el-input>
                    </el-form-item>
                </el-col>   -->
        <el-col :span="8">
          <el-form-item label="业务流水号">
            <el-input v-model="form.sblsh"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理(立案)时间">
            <el-input v-model="form.createdate"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办结时间">
            <el-input v-model="form.finishDate"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="受理(承办)单位">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态">
            <el-input v-model="form.fileStatusName" disabled></el-input>
          </el-form-item>
        </el-col>
        <div v-if="this.form.Custom">
          <el-col :span="24">
            <p class="text">自定义</p>
          </el-col>
          <div v-for="(item, index) in form.Custom" :key="index">
            <el-col :span="8">
              <el-form-item :label="item.note">
                <el-input v-model="item.settingValues"></el-input>
              </el-form-item>
            </el-col>
          </div>
        </div>
      </el-form>
    </el-row>
  </div>
</template>
<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => {
      },
    },
  },
  created() {
    let retenName = "";
    if (this.form.retenName.lastIndexOf("年") > 0) {
      retenName = this.form.retenName.replace("年", "");
    } else if (this.form.retenName.lastIndexOf("永久") > 0) {
      retenName = this.form.retenName.replace("永久", "");
    }
    let name = this.form.type + retenName;
    if (this.form.isFile == 0) {
      this.form.fileStatusName = "未采集";
    } else if (this.form.isFile == 1) {
      this.form.fileStatusName = "已采集";
    } else if (this.form.isFile == 2) {
      this.form.fileStatusName = "已生成信息包";
    }
    if (this.form.archivesNo) {
      this.form.no =
          "YGD-" +
          this.form.bmbm +
          "-" +
          this.form.taskCode +
          "-" +
          this.form.year +
          "-" +
          name +
          "-" +
          this.form.archivesNo;
    }
  },
};
</script>
<style scoped>
.el-scrollbar__wrap {
  overflow-x: hidden;
}

.text {
  font-size: 18px;
  margin-left: 60px;
  color: red;
}
</style>
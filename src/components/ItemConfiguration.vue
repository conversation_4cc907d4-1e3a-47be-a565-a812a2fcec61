<template>
  <div>
    <el-descriptions title="类别号配置">
      <el-descriptions-item label="类别号名称">默认类别号</el-descriptions-item>
      <el-descriptions-item label="规则">默认规则</el-descriptions-item>

      <el-descriptions-item label="状态">
        <el-tag size="small">已启用</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="描述"
        >11位部门编码+事项代码（2位事项类型代码+5位事项主项代码+3位事项子项代码）+年度+保管期限+归档信息包流水号
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="案卷号配置">
      <el-descriptions-item label="案卷号配置名称"
        >默认案卷号</el-descriptions-item
      >
      <el-descriptions-item label="规则">默认规则一</el-descriptions-item>

      <el-descriptions-item label="状态">
        <el-tag size="small">已启用</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="描述"
        >案卷号按从1开始，每接收一个案卷自动加1。
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="档号配置">
      <el-descriptions-item label="档号配置名称"
        >默认案卷号</el-descriptions-item
      >
      <el-descriptions-item label="规则">默认规则一</el-descriptions-item>

      <el-descriptions-item label="状态">
        <el-tag size="small">已启用</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="描述"
        >全宗号+类别号+年度+案卷号
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="scss" scoped></style>

<template>
  <div>
    <el-row :gutter="24" v-for="(item,index) in form">
      <el-form ref="form" :model="item" label-width="200px">

        <div style="margin-left: 20px;font-weight: bolder"></div>
        <div style="margin-left: 20px;font-weight: bolder">环节：{{ index + 1 }} 环节名称：补正</div>
        <el-col :span="6">
          <el-form-item label="业务流水号">
            <el-input v-model="item.sblsh" readonly placeholder="业务流水号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正告知发出人姓名">
            <el-input v-model="item.informerName" readonly placeholder="补正告知发出人姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正告知部门所在地行政区划代码">
            <el-input v-model="item.informRegionCode" readonly placeholder="补正告知部门所在地行政区划代码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正告知部门统一社会信用代码">
            <el-input v-model="item.informOrgCode" readonly placeholder="补正告知部门统一社会信用代码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正告知部门名称">
            <el-input v-model="item.informOrgName" readonly placeholder="补正告知部门名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正告知部门名称">
            <el-input v-model="item.correctionReason" readonly placeholder="补正告知部门名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正告知时间">
            <el-input v-model="item.informTime" readonly placeholder="补正告知时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正受理时间">
            <el-input v-model="item.acceptTime" readonly placeholder="补正受理时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正受理人姓名">
            <el-input v-model="item.acceptName" readonly placeholder="补正受理人姓名"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="补正受理部门所在地行政区划代码">
            <el-input v-model="item.acceptRegionCode" readonly placeholder="补正受理部门所在地行政区划代码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正受理部门统一社会信用代码">
            <el-input v-model="item.acceptOrgCode" readonly placeholder="补正受理部门统一社会信用代码"
                      readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正受理部门名称">
            <el-input v-model="item.acceptOrgName" readonly placeholder="补正受理部门名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="补正受理具体地点">
            <el-input v-model="item.acceptAddress" readonly placeholder="补正受理具体地点"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>

  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Array,
      default: () => {
      },
    },
  },
};
</script>

<style lang="scss" scoped>

</style>

<template>
  <div>
    <el-row :gutter="24" v-for="(item,index) in form">
      <el-form ref="form" :model="item" label-width="200px">

        <div style="margin-left: 20px;font-weight: bolder"></div>
        <div style="margin-left: 20px;font-weight: bolder">环节：{{ index + 1 }} 环节名称：特别程序</div>
        <el-col :span="6">
          <el-form-item label="业务流水号">
            <el-input v-model="item.sblsh" readonly placeholder="业务流水号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特别程序种类名称">
            <el-input v-model="item.specialname" readonly placeholder="特别程序种类名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特别程序开始时间">
            <el-input v-model="item.specialtime" readonly placeholder="特别程序开始时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特别程序启动理由或依据">
            <el-input v-model="item.specialreason" readonly placeholder="特别程序启动理由或依据"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="申请人">
            <el-input v-model="item.applyerusername" readonly placeholder="申请人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特别程序结束时间">
            <el-input v-model="item.endtime" readonly placeholder="特别程序结束时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特别程序结果">
            <el-input v-model="item.result" readonly placeholder="特别程序结果"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特别程序办理意见">
            <el-input v-model="item.specialexplain" readonly placeholder="特别程序办理意见"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="办理人">
            <el-input v-model="item.handleuser" readonly placeholder="办理人"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="特别程序收费金额">
            <el-input v-model="item.charge" readonly placeholder="特别程序收费金额"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特别程序金额单位代码">
            <el-input v-model="item.monetaryunitcode" readonly placeholder="特别程序金额单位代码"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>

  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Array,
      default: () => {
      },
    },
  },
};
</script>

<style lang="scss" scoped>

</style>

<template>
  <div class="box">
    <div class="searchDiv">
      <el-table :data="tableData" :height="tableHeight" :header-cell-style="{ background: '#F6F7FA' }">
        <el-table-column label="序号" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="taskType" label="事项类型"
                         :formatter="(row, column) => $getDict(row, column, 'taskType')"
                         align="center"></el-table-column>
        <el-table-column prop="receiptName" label="收据名称" align="center"></el-table-column>
        <el-table-column prop="receiptType" label="收据类型"
                         :formatter="(row, column) => $getDict(row, column, 'receiptType')"
                         align="center"></el-table-column>
        <el-table-column prop="zipPackage" label="所属目录" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.zipPackage != null">{{ scope.row.zipPackage.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <!--            <el-button type="text" icon="el-icon-delete" @click="handleRemove(scope.$index, scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>


      <el-dialog :title="receiptCatalogConfigDialog.title" width="50%"
                 :visible.sync="receiptCatalogConfigDialog.dialogVisible" :before-close="handleClose"
                 center :append-to-body="true" :close-on-click-modal="false">
        <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="auto"
                 class="demo-ruleForm">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="收据名称" prop="name">
                <el-input v-model="ruleForm.receiptName" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目录归属" prop="zipPackageID">
                <el-select v-model="ruleForm.zippackageId" clearable filterable placeholder="请选择"
                           style="width: 100%">
                  <el-option v-for="item in zipPackage" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
        <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
        </span>
      </el-dialog>
    </div>
  </div>


</template>

<script>
import {findAllByTaskType, save, remove} from "@/api/receiptCatalogConfig";
import {zipPackageApi} from "@/api/zipPackage";


export default {
  props: {
    taskType: String //事项类型代码
  },
  data() {
    return {
      tableHeight: 0,
      tableData: [],
      zipPackage: [],
      receiptCatalogConfigDialog: {
        title: "",
        dialogVisible: false
      },
      ruleForm: {}
    }
  },
  mounted() {
    this.initTableHeight();
  },
  created() {
    this.getInitData();
    this.getTableTata()
  },
  methods: {
    //初始话基础数据
    getInitData() {
      Promise.all([
        new Promise((rs, rp) => {
          zipPackageApi.getDatasArchivesType({archivesType: this.taskType}).then((a) => {
            rs(a);
          });
        }),
      ]).then((a) => {
        this.zipPackage = a[0].data;
      });
    },
    getTableTata() {
      findAllByTaskType({taskType: this.taskType}).then(res => {
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      this.$nextTick(() => {
        let tempHeight = height - 45 - 50 - 51;
        this.tableHeight = tempHeight;
      });
    },
    handleEdit(index, row) {
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.receiptCatalogConfigDialog.title = "编辑";
      this.receiptCatalogConfigDialog.dialogVisible = true
    },
    handleRemove(index, row) {
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        remove(row.id).then(res => {
          if (res.code == 200) {
            this.$message.success("删除成功");
            this.getTableTata()
          }
        })
      }).catch(() => {
      })
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          save(this.ruleForm).then((a) => {
            if (a.code === 200) {
              this.$message.success("保存成功");
              this.getTableTata();
            } else {
              this.$message.error(a.msg);
            }
            this.receiptCatalogConfigDialog.dialogVisible = false

          }).catch(err => {
            this.$message.error("网络异常,请稍后重试");
          });
        } else {
          return false;
        }
      });
    },
    cancel() {
      this.receiptCatalogConfigDialog.dialogVisible = false
    }
  }
}
</script>

<style scoped>

</style>
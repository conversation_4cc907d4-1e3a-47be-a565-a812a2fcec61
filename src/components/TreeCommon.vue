<template>
  <div class="div-tree" style="margin-top: 10px;">
    <el-input placeholder="输入关键字进行过滤" v-model="filterText" size="small"></el-input>
    <el-tree  :style="{height: scrollerHeight,overflow:'auto'}"  @node-click="nodeClick" class="filter-tree" :data="data" lazy highlight-current :load="loadData"
      :props="defaultProps"  :filter-node-method="filterNode" ref="tree" :default-expand-all="true">
      <div class=" el-tree-node" slot-scope="{ node, data }" style="margin-bottom: 10px;">
        <div class=" el-tree-node__content">
          <span class="custom-tree-node">
            <span>
              <span class="vertical-align">
                {{ node.label }}
              </span>
              <span v-if="data.count > 0" class="vertical-align" style="text-align: right;color: rgb(0,0,255);font-weight: 800">
               ({{ data.count }})
              </span>
            </span>
          </span>
        </div>
      </div>
    </el-tree>
  </div>
</template>
<script>
import { departApi } from "@/api/department";

export default {
  props:{
    isFile: String,
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getData();
    if(JSON.parse(localStorage.getItem("data"))){
      this.data.push(JSON.parse(localStorage.getItem("data")))
      localStorage.removeItem("data")
    }
  },
  methods: {
    loadData(node, resolve) {
      if (!node.data.id) {
        return resolve([]);
      }
      this.type = node.level;
      if (node.level === 1) {
        let array = [];
        departApi.getDataByDepartment({ bmbm: node.data.id ,isFile:this.isFile}).then((a) => {
          array = [];
          a.data.forEach((c) => {
            let obj = {
              id: c.taskCode,
              name: c.taskName,
              count: c.count,
              type: 1
            };
            array.push(obj);
          });
          return resolve(array);
        });
      }
      if (node.level === 2) {
        return resolve([]);
      }
    },
    //筛选结点
    filterNode(value, data, node) {
      if (!value) return true;
      return this.findSearKey(node, value);
    },
    //递归搜索父级是否包含关键字
    findSearKey(node, key) {
      if (node.label.indexOf(key) !== -1) {
        return true;
      } else {
        if (node.parent.parent == null) {
          return false;
        } else {
          return this.findSearKey(node.parent, key);
        }
      }
    },
    /*filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
      // if (!value) return true;
      // let parentNode = node.parent
      // labels = [node.label]
      // level = 1
      // while( level < node.level){
      //   labels = [...labels, parentNode.label]
      //   parentNode = parentNode.parent
      //   level++
      // }
      // return labels.some(label => label.indexOf(value) !== -1)
    },*/
    nodeClick(data, node, obj) {
      this.$emit("nodeClick", data);
    },
    getData() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text:"正在加载数据中...",
        fullscreen:true
      });
      departApi
        .departmentTree()
        .then((a) => {
          this.data = [];
          a.data.forEach(v =>{
            let obj = {
              id: v.extOrgTyshxydm,
              name: v.groupName,
              type:0
            };
            this.data.push(obj);
          })

          loading.close();
        })
        .catch((a) => {
          //成功回调函数停止加载
          loading.close();
        });
    },
  },
  computed: {
    scrollerHeight: function() {
      return (window.innerHeight - 85) + 'px';
    }
  },
  data() {
    return {
      type: 0,
      filterText: "",
      data: [],
      defaultProps: {
        children: "children",
        label: "name",
      },

    };
  },

};
</script>
<style scoped>
/deep/ .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: wheat !important;
}
/deep/.el-tree {
  width: 100%;
  overflow: scroll;
}
/deep/.el-tree>.el-tree-node {
  display: inline-block;
  min-width: 100%;
}



</style>

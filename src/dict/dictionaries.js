import DataDictionary from "@/dict/DataDictionary";

export function dictComputed(dictionary, valueField) {
    return {
        get() {
            return dictionary.get(valueField)
        },
        set(value) {
            valueField = Object.keys(dictionary.getAll().find(key => dictionary.get(key) === value))
        }
    }
}


/**
 * 是否镇街
 * @type {DataDictionary}
 */
export const isTown = new DataDictionary({
    0: '否',
    1: '是',
})

/**
 * 是否代办
 * @type {DataDictionary}
 */
export const isAgent = new DataDictionary({
    0: '否',
    1: '是',
})
/**
 * 证件类型
 * @type {DataDictionary}
 */
export const cardTypeDict = new DataDictionary({
    10: '身份证',
    11: '军官证',
    12: '士兵证',
    13: '警官证',
    14: '港澳居民来往内地通行证',
    15: '台湾居民来往大陆通行证',
    16: '香港身份证',
    17: '澳门身份证',
    18: '台湾身份证',
    20: '护照',
    40: '其他有效个人身份证件',
    49: '统一社会信用代码',
    50: '组织机构代码证',
    51: '营业执照',
    52: '事业单位登记证书',
    53: '社团登记证书',
    54: '民办非企业单位登记证书',
    55: '工会法人资格证书',
    60: '税务登记证',
    80: '其他有效机构身份证件',
})
/**
 * 办件类型
 * @type {DataDictionary}
 */
export const applyType = new DataDictionary({
    1: '窗口登记',
    2: '网上申报',
})
/**
 * 申报来源
 * @type {DataDictionary}
 */
export const applyinstSource = new DataDictionary({
    'win': '窗口申报',
    'net': '网上申报',
})
/**
 * 办件类型
 */
export const projectType = new DataDictionary({
    '1': '即办件',
    '2': '承诺件',
    '3': '上报件',
})
/**
 *
 */
export const resultCetrType = new DataDictionary({
    '1': "纸质结果件",
    '2': "电子结果件",
    '3': "网上公示网上公告",
    '4': "无结果件",
})
/**
 * 审批文书对应环节
 * @type {DataDictionary}
 */
export const paperDealNode = new DataDictionary({
    '3': "申办",
    '4': '网上预受理',
    '5': '受理',
    '6-1': '承办',
    '6-2': '审核',
    '6-3': '批准',
    '7': '补正告知',
    '8': '补正受理',
    '9': '特别程序申请',
    '10': '特别程序结果',
    '11': '办结',
    '11-1': '不通过办结',
    '12': '制证',
    '13': '领取登记',
})
import request from '../utils/request'
const page = query => {
    return request({
        url: '/materialsBillApi/getData',
        method: 'get',
        params: query
    });
};



const getReadyFiles = query => {
    return request({
        url: '/materialsBillApi/getReadyFiles',
        method: 'get',
        params: query
    });
};
const remove = query => {
    return request({
        url: '/materialsBillApi/remove',
        method: 'get',
        params: query
    });
};
const syncMaterialBill = query => {
    return request({
        url: '/materialsBillApi/syncMaterialBill',
        method: 'get',
        params: query
    });
}; 
const getMaterialBillTree = query => {
    return request({
        url: '/materialsBillApi/getMaterialBillTree',
        method: 'get',
        params: query
    });
};


const save = query => {
    return request({
        url: '/materialsBillApi/save',
        method: 'post',
        data: query
    });
};

const getSolidifyList = query => {
    return request({
        url: '/materialsBillApi/getSolidifyList',
        method: 'get',
        params: query
    });
};

const solidifySave = query => {
    return request({
        url: '/materialsBillApi/solidifySave',
        method: 'POST',
        data: query
    });
};
const solidifyRemove = query => {
    return request({
        url: '/materialsBillApi/solidifyRemove',
        method: 'GET',
        params: query
    });
};
const syncSolidify = query => {
    return request({
        url: '/materialsBillApi/syncSolidify',
        method: 'GET',
        params: query
    });
};

export const materialsBillApi={
    page,
    save,
    remove,
    syncMaterialBill,
    getMaterialBillTree,
    getReadyFiles,
    getSolidifyList,
    solidifySave,
    solidifyRemove,
    syncSolidify
}
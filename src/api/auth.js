// http://localhost:8085/api/oauth/token?client_id=business-client&client_secret=user-secret-8888&grant_type=client_credentials


import request from '../utils/request'
const auth = query => {
    request({
        url: '/oauth/token?client_id=business-client&client_secret=user-secret-8888&grant_type=client_credentials',
        method: 'get',
        params: query
    }).then(a=>{
        sessionStorage.setItem("token",a.access_token)
    })
}; 
export const authApi={
    auth
}

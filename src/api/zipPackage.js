import request from '../utils/request'

const page = query => {
    return request({
        url: '/zipPackageApi/getDataArchivesTypeId',
        method: 'get',
        params: query
    });
};
const save = query => {
    return request({
        url: '/zipPackageApi/save',
        method: 'post',
        data: query
    });
};
const remove = query => {
    return request({
        url: '/zipPackageApi/delete',
        method: 'get',
        params: query
    });
};
const list = query => {
    return request({
        url: '/zipPackageApi/getDatasArchivesTypeId',
        method: 'get',
        params: query
    });
};
const getDatasArchivesType = query => {
    return request({
        url: '/zipPackageApi/getDatasArchivesType',
        method: 'get',
        params: query
    });
};
const getAll = query => {
    return request({
        url: '/zipPackageApi/getAll',
        method: 'get',
    });
};
export const zipPackageApi = {
    page,
    save,
    remove,
    list,
    getAll,
    getDatasArchivesType
}

import request from "../utils/request";

//查询xml
export function getAll(query) {
  return request({
    url: "/archivesAttributeApi/getData",
    method: "get",
    params: query,
  });
}
//选择框获取数据
export function findSbjcAll(query) {
  return request({
    url: "/sbjcVoApi/getAllOfSelect",
    method: "get",
    params: query,
  });
}

export function save(form) {
  return request({
    url: "/archivesAttributeApi/insert",
    method: "post",
    data: form,
  });
}
export function remove(data) {
  return request({
    url: "/archivesAttributeApi/delete",
    method: "delete",
    params:data
  });
}


import request from '../utils/request'
const page = query => {
    return request({
        url: '/itemCodeApi/getData',
        method: 'get',
        params: query
    });
};
const save = query => {
    return request({
        url: '/UnitsApi/save',
        method: 'post',
        data: query
    });
};
const itemCodeSave = query => {
    return request({
        url: '/itemCodeApi/save',
        method: 'post',
        data: query
    });
};
const syncItemCode = query => {
    return request({
        url: '/itemBusiness/syncItemCode',
        method: 'get',
        params: query
    });
};


const remove = query => {
    return request({
        url: '/UnitsApi/remove',
        method: 'get',
        params: query
    });
};
const itemCodeRemove = query => {
    return request({
        url: '/itemCodeApi/remove',
        method: 'get',
        params: query
    });
};
const getSituationInfos = query => {
    return request({
        url: '/itemCodeApi/getSituationInfos',
        method: 'get',
        params: query
    });
};
const getAllSituationVersions = query => {
    return request({
        url: '/itemCodeApi/getAllSituationVersions',
        method: 'get',
        params: query
    });
};
const syncSituationVersions = query => {
    return request({
        url: '/itemCodeApi/syncSituationVersions',
        method: 'get',
        params: query
    });
};
const saveSituationVersions = data => {
    return request({
        url: '/itemCodeApi/saveSituationVersions',
        method: 'POST',
        data
    });
};
const exportItemCodeList = () => {
    return request({
        url: '/itemCodeApi/exportItemCodeList',
        method: 'POST',
        responseType: 'blob',

    });
};


export const itemCodeApi={
    page,
    save,
    remove,
    syncItemCode,
    itemCodeSave,
    itemCodeRemove,
    getSituationInfos,
    syncSituationVersions,
    saveSituationVersions,
    getAllSituationVersions,
    exportItemCodeList
}

import request from "../utils/request";
import quartRequest from "../utils/quartRequest";
import fileRequest from "../utils/fileRequest";
const page = (query) => {
  return request({
    url: "/itemBusiness/getItemSlData",
    method: "get",
    params: query,
  });
};
const getItemSlDataForMetaData = (query) => {
  return request({
    url: "/itemBusiness/getItemSlDataForMetaData",
    method: "get",
    params: query,
  });
};
const getItemSLEntity = (query) => {
  return request({
    url: "/itemBusiness/getItemSLEntityBySblsh",
    method: "get",
    params: query,
  });
};
const getFileBySblshAllNoPage = (query) => {
  return request({
    url: "/itemBusiness/getFileBySblshAllNoPage",
    method: "get",
    params: query,
  });
};
const getDownZipUrl = (query) => {
  return request({
    url: "/itemBusiness/getDownZipUrl",
    method: "get",
    params: query,
  });
};
const getProgressStatus = (query) => {
  return request({
    url: "/itemBusiness/getProgressStatus",
    method: "get",
    params: query,
  });
};
const pageReady = (query) => {
  return request({
    url: "/itemBusiness/getItemReadyData",
    method: "get",
    params: query,
  });
};

const getReadyFileInfo = (query) => {
  return request({
    url: "/itemBusiness/getReadyFileInfo",
    method: "get",
    params: query,
  });
};

const getFileInfoBySblsh = (query) => {
  return request({
    url: "/itemBusiness/getFileInfoBySblsh",
    method: "get",
    params: query,
  });
};
const transferZip = (query) => {
  return fileRequest({
    url: "/sendZip/sendZipBySblsh",
    method: "get",
    params: query,
  });
};

const archivesCount = (query) => {
  return request({
    url: "/itemBusiness/archivesCount",
    method: "post",
    data: query,
  });
};
const getFileUrl = (query) => {
  return request({
    url: "/itemBusiness/fileUrl",
    method: "post",
    params: query,
  });
};
const xmlFiles = (query) => {
  return fileRequest({
    url: "/filesApi/getXmlFiles",
    method: "post",
    params: query,
  });
};

const createYGDZip = (query) => {
  return fileRequest({
    url: "/filesApi/createYGDZip",
    method: "post",
    params: query,
  });
};

const uploadApi = (query) => {
  return fileRequest({
    url: "/uploadApi/uploadFile",
    method: "post",
    params: query,
  });
};

const getUploadUrl = (query) => {
  return fileRequest({
    url: "/uploadApi/getUploadUrl",
    method: "post",
    params: query,
  });
};
/**
 * 同步镇街事项
 * @param data
 * @returns {*}
 */
const syncZjItemCode = query => {
  return request({
    url: '/itemBusiness/syncZjItemCode',
    method: 'POST',
    params:query
  });
};
const localZjItemCode = query => {
  return request({
    url: '/itemBusiness/localZjItemCode',
    method: 'POST',
    params:query
  });
};
const saveZjItemCode = data => {
  return request({
    url: '/itemBusiness/saveZjItemCode',
    method: 'POST',
    data
  });
};
const deleteZjItemCode = data => {
  return request({
    url: '/itemBusiness/deleteZjItemCode',
    method: 'POST',
    data
  });
};
/**
 * 获取统计数据
 * @param data
 * @returns {*}
 */
const findDataStatistics = query => {
  return request({
    url: '/itemBusiness/findDataStatistics',
    method: 'GET',
    params:query
  });
};

/**
 * 重新同步流水号
 * @param query
 * @returns {*}
 */
const reSync = query => {
  return quartRequest({
    url: '/sync/reSync',
    method: 'POST',
    params:query
  });
};


export const itemBusinessApi = {
  page,
  getItemSlDataForMetaData,
  transferZip,
  getItemSLEntity,
  getFileBySblshAllNoPage,
  getDownZipUrl,
  getProgressStatus,
  pageReady,
  getReadyFileInfo,
  getFileInfoBySblsh,
  archivesCount,
  getFileUrl,
  xmlFiles,
  createYGDZip,
  uploadApi,
  getUploadUrl,
  syncZjItemCode,
  localZjItemCode,
  saveZjItemCode,
  deleteZjItemCode,
  findDataStatistics,
  reSync
};

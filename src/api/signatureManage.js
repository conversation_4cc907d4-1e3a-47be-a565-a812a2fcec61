import request from '../utils/request'

export function getAll() {
    return request({
        url: "authGroup/getAll",
        method: "get",
    })
}

export function signatureManage(query) {
    return request({
        url: "signatureManage/getAll",
        method: "get",
        params: query
    })
}

export function getAdd(data) {
    return request({
        url: "signatureManage",
        method: "POST",
        data
    })
}

export function remove(id) {
    return request({
        url: "signatureManage/" + id,
        method: "DELETE",
    })
}

export function genFileWaterSignatureFilePath(data) {
    return request({
        url: "signatureManage/genFileWaterSignatureFilePath",
        method: "POST",
        data
    })
}

export function removeTestFile() {
    return request({
        url: "signatureManage/removeTestFile",
        method: "get",
    })
}
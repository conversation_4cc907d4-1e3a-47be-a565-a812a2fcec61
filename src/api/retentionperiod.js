import request from '../utils/request'
const page = query => {
    return request({
        url: '/retentionPeriodApi/getData',
        method: 'get',
        params: query
    });
};
const list = query => {
    return request({
        url: '/retentionPeriodApi/getAll',
        method: 'get',
        params: query
    });
};
const save = query => {
    return request({
        url: '/retentionPeriodApi/insert',
        method: 'post',
        data: query
    });
};
const remove = query => {
    return request({
        url: '/retentionPeriodApi/delete',
        method: 'get',
        params: query
    });
};
export const retentionperiodApi={
    page,
    save,
    remove,
    list
}

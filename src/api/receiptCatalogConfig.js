import request from "@/utils/request";

/**
 * 通过事项类型查询配置
 * @param data
 * @returns {*}
 */
export function findAllByTaskType(data) {
    return request({
        url: "receiptCatalogConfigApi/findAllByTaskType",
        method: "GET",
        params: data
    })
}

/**
 * 更新数据
 * @param data
 * @returns {*}
 */
export function save(data) {
    return request({
        url: "receiptCatalogConfigApi/save",
        method: "POST",
        data
    })
}

/**
 * 更新数据
 * @param data
 * @returns {*}
 */
export function remove(data) {
    return request({
        url: "receiptCatalogConfigApi/" + data,
        method: "DELETE",
    })
}

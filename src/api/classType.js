import request from '../utils/request'
const page = query => {
    return request({
        url: '/classTypeApi/getData',
        method: 'get',
        params: query
    });
};
const list = query => {
    return request({
        url: '/classTypeApi/getAll',
        method: 'get',
        params: query
    });
};
const save = query => {
    return request({
        url: '/classTypeApi/save',
        method: 'post',
        data: query
    });
};
const remove = query => {
    return request({
        url: '/classTypeApi/delete',
        method: 'get',
        params: query
    });
};
export const classTypeApi={
    page,
    save,
    remove,
    list
}

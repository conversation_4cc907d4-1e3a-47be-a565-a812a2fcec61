import request from '../utils/request'

export function page(data) {
   return  request({
        url: "departmentSignature",
        method:"get",
        params:data
    })
}
export function departments() {
   return  request({
        url: "/UnitsApi/departmentTree",
        method:"get",
    })
}
export function save(data) {
   return  request({
        url: "departmentSignature",
        method:"post",
       data
    })
}
export function remove(data) {
   return  request({
        url: "departmentSignature",
        method:"delete",
        params:data
    })
}
import request from '../utils/request'

const page = query => {
    return request({
        url: '/archivesTypeApi/getData',
        method: 'get',
        params: query
    });
};
const save = query => {
    return request({
        url: '/archivesTypeApi/save',
        method: 'post',
        data: query
    });
};
const getDataByTypeName = query => {
    return request({
        url: '/archivesTypeApi/getDataByItemCode',
        method: 'GET',
        params: query
    });
};
const remove = query => {
    return request({
        url: '/archivesTypeApi/delete',
        method: 'get',
        params: query
    });
};
const getAll = query => {
    return request({
        url: '/archivesTypeApi/getAll',
        method: 'get',
    });
};
export const archivesTypeApi = {
    page,
    save,
    remove,
    getDataByTypeName,
    getAll
}

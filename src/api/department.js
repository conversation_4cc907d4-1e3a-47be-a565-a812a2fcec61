import request from '../utils/request'
import qs from 'qs'

const page = query => {
    return request({
        url: '/UnitsApi/getDepartment',
        method: 'get',
        params: query
    });
};

const getDataByDepartment = query => {
    return request({
        url: '/itemCodeApi/getDataByDepartment',
        method: 'get',
        params: query
    });
}


const save = query => {
    return request({
        url: '/UnitsApi/save',
        method: 'post',
        data: query,
    });
};
const departmentTree = query => {
    return request({
        url: '/UnitsApi/departmentTree',
        method: 'get',
        params: query
    });
};


const remove = query => {
    return request({
        url: '/UnitsApi/remove',
        method: 'get',
        params: query
    });
};
const syncData = query => {
    return request({
        url: '/UnitsApi/syncUnitData',
        method: 'get',
        params: query
    });
};
const getAllByUnitsId = query => {
    return request({
        url: '/departmentZipPackage/getAllByUnitsId',
        method: 'get',
        params: query
    });
};

export const departApi = {
    page,
    save,
    remove,
    departmentTree,
    syncData,
    getDataByDepartment,
    getAllByUnitsId
}

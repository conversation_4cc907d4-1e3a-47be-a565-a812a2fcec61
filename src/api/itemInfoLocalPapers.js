import request from "@/utils/request";

export function localPapersPage(data) {
    return request({
        url: "situationLocalPapersApi/localPapersList",
        method: "GET",
        params: data
    })
}

/**
 * 同步审批文书
 * @param data
 * @returns {*}
 */
export function syncLocalPapers(data) {
    return request({
        url: "situationLocalPapersApi/syncLocalPapers",
        method: "GET",
        params: data
    })
}

/**
 * 保存审批文书
 * @param data
 * @returns {*}
 */
export function saveLocalPapers(data) {
    return request({
        url: "situationLocalPapersApi/save",
        method: "POST",
        data
    })
}

/**
 * 删除审批文书
 * @param data
 * @returns {*}
 */
export function removeLocalPapers(id) {
    return request({
        url: "situationLocalPapersApi/remove/" + id,
        method: "DELETE",
    })
}
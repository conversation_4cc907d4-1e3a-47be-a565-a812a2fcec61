import request from "../utils/request";
import fileRequest from "../utils/fileRequest";
import quartzRequest from "../utils/quartRequest";

export function getBasicData(query) {
  return request({
    url: "/itemBusiness/getBasicData",
    method: "get",
    params: query,
  });
}

export function getBzData(query) {
  return request({
    url: "/itemBusiness/getBzData",
    method: "get",
    params: query,
  });
}

export function getSLData(query) {
  return request({
    url: "/itemBusiness/getSlData",
    method: "get",
    params: query,
  });
}

export function getTbcxData(query) {
  return request({
    url: "/itemBusiness/getTbcxData",
    method: "get",
    params: query,
  });
}

export function getGcData(query) {
  return request({
    url: "/itemBusiness/getGcData",
    method: "get",
    params: query,
  });
}

export function getBjData(query) {
  return request({
    url: "/itemBusiness/getBjData",
    method: "get",
    params: query,
  });
}

export function getCzData(query) {
  return request({
    url: "/itemBusiness/getCzData",
    method: "get",
    params: query,
  });
}

export function getAllFilePaths(query) {
  return request({
    url: "/itemBusiness/getAllFilePaths",
    method: "get",
    params: query,
  });
}

export function getGdData(query) {
  return request({
    url: "/itemBusiness/getItemData",
    method: "get",
    params: query,
  });
}

export function buildExcelDocument2(query) {
  return fileRequest({
    url: "/downloadApi/buildExcelDocument2",
    method: "post",
    params: query,
    responseType: "blob",
  });
}

export function uploadFile(data) {
  return fileRequest({
    url: "/uploadApi/uploadFile",
    method: "post",
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
      'Access-Control-Allow-Origin':'*'
    },
  });
}
export function removeFile(data) {
  return fileRequest({
    url: "/uploadApi/removeFile",
    method: "post",
    data: data,
  });
}
export function FourChecks(query) {
  return request({
    url: "/FourChecks/getAll",
    method: "get",
    params: query
  });
}

export function selectItemCodeFourChecked(query) {
  return request({
    url: "/FourChecks/itemCodeFourChecked",
    method: "get",
    params: query
  });
}

export function putFourCheckSetting(data) {
  return request({
    url: "/FourChecks/putFourCheckSetting",
    method: "post",
    data
  });
}

export function getFourCheckSetting() {
  return request({
    url: "/FourChecks/getFourCheckSetting",
    method: "get",

  });
}

export function deleteFourCheck(query) {
  return request({
    url: "/FourChecks/deleteFourCheck",
    method: "get",
    params: query
  });
}

export function updateFourCheck(data) {
  return request({
    url: "/FourChecks/updateFourCheck",
    method: "post",
    data
  });
}

export function getMatedateAll(query) {
  return request({
    url: "/MetadataFig/getMatedateAll",
    method: "get",
    params: query
  });
}

export function addMatedate(data) {
  return request({
    url: "/MetadataFig/addMatedate",
    method: "post",
    data
  });
}

export function deleteMatedate(query) {
  return request({
    url: "/MetadataFig/deleteMatedate",
    method: "get",
    params: query
  });
}

/**
 * 添加元数据
 * @param data
 * @returns {*}
 */
export function addCustomStting(data) {
  return request({
    url: "/MetadataFig/addCustomStting",
    method: "post",
    data
  });
}

/**
 * 保存申办基础信息
 * @param data
 * @returns {*}
 */
export function saveSbjcData(data) {
  return request({
    url: "/itemBusiness/saveSbjcData",
    method: "post",
    data
  });
}
/**
 * 保存受理信息
 * @param data
 * @returns {*}
 */
export function saveItemSlData(data) {
  return request({
    url: "/itemBusiness/saveItemSl",
    method: "post",
    data
  });
}

/**
 * 保存过程信息
 * @param data
 * @returns {*}
 */
export function saveItemGcData(data) {
  return request({
    url: "/itemBusiness/saveItemGc",
    method: "post",
    data
  });
}
/**
 * 保存办结信息
 * @param data
 * @returns {*}
 */
export function saveItemBjData(data) {
  return request({
    url: "/itemBusiness/saveItemBj",
    method: "post",
    data
  });
}
/**
 * 保存归档配置信息
 * @param data
 * @returns {*}
 */
export function saveItemCodeData(data) {
  return request({
    url: "/itemBusiness/saveItemCode",
    method: "post",
    data
  });
}
export function getResults(query) {
  return request({
    url: "/FourChecks/getCheckResultBySblsh",
    method: "get",
    params: query
  });
}

export function fontManage(data) {
  return request({
    url: "/fontManage",
    method: "post",
    data
  });
}

export function getFontNames() {
  return request({
    url: "/fontManage/getFontNames",
    method: "get",
  });
}

export function fontManage2() {
  return request({
    url: "/fontManage",
    method: "get",
  });
}

export function fontManage3(query) {
  return request({
    url: "/fontManage",
    method: "DELETE",
    params:query
  });
}

export function getUploadFileInfo(query) {
  return request({
    url: "logApi/getUploadFileInfo",
    method: "GET",
    params:query
  });
}
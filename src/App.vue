<template>
  <div id="app" >
    <div id="nav">
      <!-- <router-link to="/">Home</router-link> |
      <router-link to="/about">About</router-link> -->
    </div>
    <!--    <div v-show="this.$store.state.show" class="big">
          <div
              class="loading"
              v-loading="loading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)"
          ></div>
        </div>-->
    <keep-alive>
      <!-- 如果当前打开页面的路由中 keepAlive: true （开启了缓存时） -->
      <router-view v-if="$route.meta.keepAlive"></router-view>
    </keep-alive>

    <!-- 如果当前打开页面的路由中 没有 或者为 keepAlive: false （关闭缓存时[默认就是false]） -->
    <router-view v-if="!$route.meta.keepAlive"></router-view>

    <!-- <keep-alive>
      <router-view />
    </keep-alive> -->

  </div>
</template>
<script>
import { getToken } from "./api/login";

export default {
  data() {
    return {
      code: "",
      url: ""
    };
  },
  methods: {
    getAccessToken() {
      //开发环境跳过
      if (process.env.NODE_ENV === 'development') {
        console.log('开发环境，跳过授权流程');
        return;
      }
      
      let paramInfo = location.href.split("?")[1];
      let param = new URLSearchParams("?" + paramInfo);
      this.code = param.get("code")
      console.log("授权码:", this.code)
      if (this.code) {
        this.url = location.href.split('?')[0]
        getToken({code: this.code, url: this.url}).then(res => {
          localStorage.setItem("Authorization", res.data.accessToken)
        })
      } else {
        this.$message.info("获取授权码失败")
      }

    },
  },
  created() {
    this.getAccessToken();
  }
};
</script>

<style>
::-webkit-scrollbar {
  height: 10px;
  width: 10px;
}
::-webkit-scrollbar-thumb {
  /*#eaecf1*/
  background-color: rgb(133, 133, 133);
  border-radius: 3px;
}
.el-tree {
  display: inline-block;
  overflow-y: auto;
  min-width: 100%;
}

</style>

<template>
<!-- 事项类型 -->
    <div class="box">
        <div class="searchDiv">
            <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
                <el-form-item label="部门编码">
                    <el-input v-model="query.bmbm" placeholder="部门编码"></el-input>
                </el-form-item>
                <el-form-item label="部门名称">
                <el-input v-model="query.name" placeholder="部门名称"></el-input>
                </el-form-item>
                <el-form-item label="全宗号">
                <el-input v-model="query.qzh" placeholder="全宗号"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table :data="tableData" style="width: 100%" height="700" border>
            <el-table-column prop="bmbm" label="部门编码"   align="center"></el-table-column> 
            <el-table-column prop="name" label="部门名称"  align="center"></el-table-column> 
            <el-table-column prop="qzh" label="全宗号"   align="center"></el-table-column> 
            <el-table-column prop="createDate" label="创建时间"   align="center"></el-table-column>  
            <el-table-column label="操作"  align="center">
                <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-edit" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                    <el-button type="text" icon="el-icon-edit" @click="handleRemove(scope.$index, scope.row)">删除</el-button>
                    <el-button type="text" icon="el-icon-edit" @click="handleViewItemCode(scope.$index, scope.row)">事项</el-button>
                </template>
            </el-table-column> 
        </el-table>
        <el-pagination 
            @size-change="handleSizeChange" 
            @current-change="handleCurrentChange" 
            :current-page.sync="query.pageNo" 
            :page-size="query.pageSize"
            layout="total, prev, pager, next"
            :total="query.total">
        </el-pagination>
        <!-- 部门编辑 -->
        <el-dialog :title="dialog.title" fullscreen :visible.sync="dialog.dialogVisible"  :before-close="handleClose" center>
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" size="small" label-width="100px" class="demo-ruleForm">
                <el-form-item label="部门编码" prop="bmbm">
                    <el-input  v-model="ruleForm.bmbm" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="部门名称" prop="name">
                    <el-input   v-model="ruleForm.name" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="全宗号"  prop="qzh">
                    <el-input v-model="ruleForm.qzh"></el-input>
                </el-form-item> 
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </span>
        </el-dialog>
        <!-- 事项列表 -->
        <el-dialog fullscreen :title="itemCodeDialog.title" :visible.sync="itemCodeDialog.dialogVisible"  :before-close="handleClose" center>
            <itemCode></itemCode>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import itemCode from '../components/ItemCode.vue'
import {departApi} from '../api/department'
export default {
    components:{
        itemCode
    },
    data(){
        return{
            query:{
                pageNo:0,
                pageSize:100,
                total:0
            },
            itemCodeDialog:{
                title:'事项',
                dialogVisible:false
            },
            dialog:{
                title:'新增',
                dialogVisible:false
            },
            rules:{ 
                bmbm:[
                      { required: true, message: '请输入部门编码', trigger: 'blur' } 
                ],
                name:[
                      { required: true, message: '请输入部门名称', trigger: 'blur' } 
                ],
                qzh:[
                      { required: true, message: '请输入全宗号', trigger: 'blur' } 
                ]
            },
            ruleForm:{},
            tableData:[]
        }
    },
    created(){
        this.getData();
    },
    methods:{
        search(){
            this.query.pageNo=0;
            this.getData();
        },
        handleEdit(index,row){
            this.dialog.title="编辑";
            this.ruleForm=row;
            this.dialog.dialogVisible=true;
        },
        handleClose(done){
            this.form={};
            done();
        },
        handleViewItemCode(index,row){
            this.itemCodeDialog.dialogVisible=true;
        },
        handleRemove(index,row){
            this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                 departApi.remove({id:row.id}).then(a=>{
                     if(a.code==200){
                        this.$message.success("删除成功");
                        this.query.pageNo=0; 
                        this.getData();
                    }else{
                            this.$message.error("删除失败");
                    }
                 });
            }).catch(() => {
                          
            });
        },
        cancel(){ 
            this.form={};
            this.dialog.dialogVisible=false;
        },
        save(formName){
         this.$refs[formName].validate((valid) => {
            if (valid) {
               departApi.save(this.ruleForm).then(a=>{
                   if(a.code==200){
                    this.$message.success("保存成功");
                    this.query.pageNo=0;
                    this.cancel();
                    this.getData();
                   }else{
                        this.$message.error("保存失败");
                   }
               })
            } else {
                console.log('error submit!!');
                return false;
            }
         })
        },
        getData(){
            departApi.page(this.query).then(a=>{
                if(a.code==200){
                    this.tableData=a.data.content;
                    this.query.total=a.data.totalElements;
                }
            })
        },
        handleSizeChange(val){
          this.query.pageNo = 0;
          this.query.pageSize = val;
            this.getData();
        },
        handleCurrentChange(val){
            this.query.pageNo=val-1;
            this.getData();
        },
    }
}
</script>
<style scoped>
.box{
    padding:10px
}
.searchDiv{
    margin-top:10px ;
}
</style>
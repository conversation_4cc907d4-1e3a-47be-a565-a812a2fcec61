<template>
  <!-- 采集日志 -->
  <div class="mote" style="padding-top: 10px">
    <el-card 
        class="collectionTree"
        style="
        margin-top: 10px;
        padding: 0px 0px;
        width: 20%;
        height: 97%;
        overflow-y: scroll;
      ">
      <div class="down-tree" ref="downTree">
        <TreeCommon @nodeClick="nodeClick"></TreeCommon>
      </div>
    </el-card>
    <el-card  style="width: 80%; margin-top: 10px; margin-left: 10px;height: 97%;">
      <div class="searchDiv1">
          <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
            <el-form-item :label="query.labelOne" v-if="query.type == 0">
              <el-input v-model="query.bmbm" placeholder="部门编码" disabled></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 0">
              <el-input v-model="query.name" placeholder="部门编码" disabled></el-input>
            </el-form-item>
            <el-form-item :label="query.labelOne" v-if="query.type == 1">
              <el-input v-model="query.taskCode" placeholder="事项编码" disabled></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 1">
              <el-input v-model="query.itemName" :placeholder="names" disabled></el-input>
            </el-form-item>
            <el-form-item label="流水号">
              <el-input v-model="query.sblsh" placeholder="流水号"></el-input>
            </el-form-item>
            <!--            <el-form-item label="电子文件号">
              <el-input
                  v-model="query.archivesNo"
                  placeholder="电子文件号"
              ></el-input>
            </el-form-item>-->
            <!-- <el-form-item label="采集类型">
              <el-select
                v-model="query.syncType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in syncType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table id="tableData" :data="tableData" class="tableClass" :height="tableHeight"
                  :header-cell-style="{ background: '#F6F7FA' }">
          <el-table-column prop="taskCode" label="序号" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="sblsh" label="流水号" align="center"></el-table-column>
          <el-table-column prop="cataLogName" label="事项名称" align="center"></el-table-column>
          <!-- <el-table-column prop="synctype" label="同步方式" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.synctype == 0">手动同步</span>
              <span v-if="scope.row.synctype == 1">自动同步</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="isFile" label="状态" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isFile == 1">已采集</div>
              <div v-else-if="scope.row.isFile == 2">已同步</div>
              <div v-else>已采集</div>
            </template>
          </el-table-column>
          <el-table-column prop="taskCode" label="事项编码" align="center"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-view" @click="handleView(scope.$index, scope.row)">查看
              </el-button>
              <!-- <el-button type="text" v-if="scope.row.IsFile==2" icon="el-icon-edit" @click="handleDown(scope.$index, scope.row)">下载</el-button>   -->
            </template>
          </el-table-column>
        </el-table>

        <el-pagination v-model:page-size="query.pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="query.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" style="float: right;margin-top: 10px;" /></el-card>
    <el-dialog :title="dialog.title" :visible.sync="dialog.dialogVisible" :fullscreen="true" :before-close="handleClose"
               center :append-to-body="true">

      <el-table id="tableData" :data="logDetailsTableData" style="width: 100%" height="550">
        <el-table-column prop="taskCode" label="序号" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="content" label="同步内容" align="center"></el-table-column>

        <el-table-column prop="result" label="结果" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.result == 0" type="success">成功</el-tag>
            <el-tag v-if="scope.row.result == 1" type="danger">失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="同步时间" align="center"></el-table-column>
        <el-table-column prop="stage" label="流程" align="center">
          <template slot-scope="scope">
            <!-- 0事项受理 1申办基础数据  2申办基础材料  3事项过程  4事现办结 5材料同步 6文件上传     -->
            <el-tag v-if="scope.row.stage == 0">事项受理</el-tag>
            <el-tag v-if="scope.row.stage == 1">申办基础数据</el-tag>
            <el-tag v-if="scope.row.stage == 2">申办基础材料</el-tag>
            <el-tag v-if="scope.row.stage == 3">事项过程</el-tag>
            <el-tag v-if="scope.row.stage == 4">事现办结</el-tag>
            <el-tag v-if="scope.row.stage == 5">材料同步</el-tag>
            <el-tag v-if="scope.row.stage == 6">文件上传</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taskCode" label="事项编码" align="center"></el-table-column>
        <el-table-column prop="taskName" label="事项名称" align="center"></el-table-column>
        <el-table-column prop="sblsh" label="流水号" align="center"></el-table-column>
      </el-table>

      <el-pagination v-model:page-size="logQuery.pageSize" :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper" :total="logQuery.total" @size-change="handleSizeLogChange"
        @current-change="handleLogCurrentChange" style="margin-top: 16px;" />
    </el-dialog>
  </div>
</template>
<script>
import TreeCommon from "../components/TreeCommon.vue";
import Business from "../components/Business.vue";
import { logApi } from "../api/log";
import { itemBusinessApi } from "../api/itemBusiness";
import { tool } from "../utils/tool";

export default {
  components: {
    TreeCommon,
    Business,
  },
  data() {
    return {
      tableHeight: 730,
      query: {
        pageNo: 0,
        pageSize: 10,
        total: 0,
        labelOne: "",
        labelTwo: "",
        type: null,
        sblsh: "",
      },
      logQuery: {
        pageNo: 0,
        pageSize: 10,
      },
      syncType: [
        { label: "手动", value: 0 },
        { label: "自动", value: 1 },
      ],
      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      rules: {
        bmbm: [{ required: true, message: "请输入部门编码", trigger: "blur" }],
        name: [{ required: true, message: "请输入部门名称", trigger: "blur" }],
        qzh: [{ required: true, message: "请输入全宗号", trigger: "blur" }],
      },
      form: {},
      tableData: [],
      logDetailsTableData: [],
      names: ''
    };
  },
  created() {
    // this.getData();
  },
  mounted() {
    this.initTableHeight();
  },
  methods: {
    parseTime(val) {
      let time = parseInt(val);
      return tool.parseTime(time);
    }, //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      let that = this;
      // 一定要使用 nextTick 来改变height 不然不会起作用
      //nav 45;
      //padding:10
      this.$nextTick(() => {
        let tempHeight = height - 45 - 70 - 51;
        // console.log(tempHeight)
        let treeDemoHeight = tempHeight + 60;
        that.$refs.downTree.style = "height:" + treeDemoHeight + "px";
        // console.log(that.$refs.downTree.style);

        this.tableHeight = tempHeight;
        console.log(this.tableHeight, "height");
      });
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    nodeClick(data) {
      console.log(data)
      this.names = data.name
      this.query.sblsh = null;
      this.query.pageNo = 0;
      this.query.type = data.type;
      //部门
      if (data.type == 0) {

        this.query.bmbm = data.id;
        this.query.name = data.name;
        this.query.itemCode = null;
        this.query.itemName = null;
        this.query.labelOne = "部门编码";
        this.query.labelTwo = "部门名称";
      }
      //事项
      if (data.type == 1) {
        this.query.bmbm = null;
        this.query.name = null;
        this.query.taskCode = data.id;
        this.query.itemName = data.name;
        this.query.labelOne = "事项编码";
        this.query.labelTwo = "事项名称";
      }
      this.getData();
    },
    handleView(index, row) {
      this.dialog.title = "查看";
      this.logQuery.sblsh = row.sblsh;
      // logApi.page2(this.logQuery).then(a=>{
      //     this.logDetailsTableData=a.data.content;
      //     this.dialog.dialogVisible=true;
      // }).catch(a=>{
      //     this.$message.error("查询失败");
      // })
      this.getLogData();
    },
    getLogData() {
      logApi
        .page2(this.logQuery)
        .then((a) => {
          this.logDetailsTableData = a.data.content;
          this.logQuery.total = a.data.totalElements;
          this.dialog.dialogVisible = true;
        })
        .catch((a) => {
          this.$message.error("查询失败");
        });
    },
    handleClose(done) {
      this.form = {};
      done();
    },
    cancel() {
      this.form = {};
      this.dialog.dialogVisible = false;
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          logApi.save(this.ruleForm).then((a) => {
            if (a.code == 200) {
              this.$message.success("保存成功");
              this.query.pageNo = 0;
              this.cancel();
              this.getData();
            } else {
              this.$message.error("保存失败");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getData() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text:"正在加载数据中...",
        target: document.querySelector("#tableData"), //loadin覆盖的dom元素节点
      });
      this.query.itemName = null;
      if (this.query.archivesNo == "") {
        this.query.archivesNo = null;
      }
      if (this.query.sblsh == "") {
        this.query.sblsh = null;
      }
      logApi
        .page(this.query)
        .then((a) => {
          if (a.code == 200) {
            this.tableData = a.data.content;
            this.query.total = a.data.totalElements;
          }
          //成功回调函数停止加载
          loading.close();
        })
        .catch((a) => {
          //成功回调函数停止加载
          loading.close();
        });
    },
    handleLogCurrentChange(val) {
      let no = val - 1;
      this.logQuery.pageNoTemp = val;
      this.logQuery.pageNo = no;
      this.getLogData();
    },
    handleSizeLogChange(val) {
      this.logQuery.pageSize = val;
      this.logQuery.pageNoTemp = 1;
      this.logQuery.pageNo = 0;
      this.getLogData();
    },
    handleSizeChange(val) {
      this.query.pageSize = val;
      this.query.pageNoTemp = 1;
      this.query.pageNo = 0;
      this.getData();
    },
    handleCurrentChange(val) {
      let no = val - 1;
      this.query.pageNoTemp = val;
      this.query.pageNo = no;
      this.getData();
    },
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}
.mote {
  display: flex;
  height: 100%;
  box-sizing: border-box;
}
.el-row {
  margin: 0px;
  padding: 0px;
}

.down-tree {
  display: block;
  /* overflow-y: scroll; */
}

.pa {
  position: absolute;
  bottom: 10px;
}

.searchDiv1 {
  margin-top: 10px;
}

/deep/ .collectionTree .el-card__body{
  padding: 0 0 0 10px;
}
</style>

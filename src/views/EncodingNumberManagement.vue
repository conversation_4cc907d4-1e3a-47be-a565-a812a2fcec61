<template>
  <div class="divBox">
    <div class="searchDiv">
      <el-form
        :inline="true"
        :model="query"
        class="demo-form-inline"
        size="small"
      >
        <el-form-item label="档号名称">
          <el-input
            v-model="query.packetName"
            placeholder="档号名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="档号规则">
          <el-input
            v-model="query.namingRule"
            placeholder="档号规则"
          ></el-input>
        </el-form-item>
        <el-form-item label="档案类别">
          <el-input
            v-model="query.archiveClass"
            placeholder="档案类别"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="query.status" placeholder="请选择">
            <el-option label="启用" value="0"></el-option>
            <el-option label="禁用" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" class="tableClass" :height="tableHeight" border>
      <el-table-column label="序号" align="center">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column
        prop="categoryName"
        label="档号名称"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="categoryRule"
        label="档号规则"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="description"
        label="描述"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="createDate"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleRemove(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="query.pageNoTemp"
      :page-size="query.pageSize"
      layout="total, prev, pager, next"
      :total="query.total"
    >
    </el-pagination>
    <!-- 规则编辑 -->
    <el-dialog
      :title="dialog.title"
      width="30%"
      :visible.sync="dialog.dialogVisible"
      :before-close="handleClose"
      center
    >
      <el-form
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="ruleForm"
        size="small"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="规则名称" prop="ruleNme">
          <el-col :span="12">
            <el-input
              v-model="ruleForm.ruleNme"
              autocomplete="off"
              placeholder="请输入规则名称"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="命名规则" prop="namingRule">
          <el-col :span="12">
            <el-input
              v-model="ruleForm.namingRule"
              autocomplete="off"
              placeholder="请输入命名规则"
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="规则描述" prop="description">
          <el-col :span="12">
            <el-input
              type="textarea"
              v-model="ruleForm.description"
              placeholder="请输入规则描述"
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="规则状态" prop="ruleStatus">
          <el-col :span="12">
            <el-switch
              v-model="ruleForm.ruleStatus"
              active-color="#13ce66"
              inactive-color="#ff4949"
            >
            </el-switch>
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import itemCode from "../components/ItemCode.vue";
import { departApi } from "../api/department";
export default {
  components: {
    itemCode,
  },
  data() {
    return {
      tableHeight: 730,
      bmbm: null,
      bmName: null,
      query: {
        pageNo: 0,
        pageSize: 10,
        total: 0,
      },
      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      rules: {
        ruleNme: [
          { required: true, message: "请输入规则名称", trigger: "blur" },
        ],
        namingRule: [
          { required: true, message: "请输入命名规则", trigger: "blur" },
        ],
      },
      ruleForm: {},
      tableData: [
        {
          id: "1",
          ruleNme: "规则一",
          namingRule: "行政编码+权力事项类型代码+年度+保管期限+申报流水号",
          createDate: "2020-01-02",
          description:
            "电子文件号实行全省统一编号规则，由行政编码+权力事项类型代码+年度+保管期限+申报流水号。",
        },
      ],
    };
  },
  created() {
    this.getData();
  },
  mounted() {
    this.initTableHeight();
  },
  methods: {
    //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      // 一定要使用 nextTick 来改变height 不然不会起作用
      //nav 45;
      //padding:10
      this.$nextTick(() => {
        let tempHeight = height - 45 - 10 - 51;
        this.tableHeight = tempHeight;

        console.log(this.tableHeight, "height");
      });
    },
    handleAdd() {
      this.dialog.title = "添加";
      this.dialog.dialogVisible = true;
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    handleEdit(index, row) {
      this.dialog.title = "编辑";
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.dialog.dialogVisible = true;
    },
    handleSync() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      departApi
        .syncData({ bmbm: this.ruleForm.bmbm, name: this.ruleForm.name })
        .then((a) => {
          this.form = a.data[0];
          loading.close();
        })
        .catch((a) => {
          loading.close();
        });
    },
    handleClose(done) {
      this.form = {};
      this.ruleForm = {};
      done();
    },
    handleViewItemCode(index, row) {
      this.bmName = row.name;
      this.bmbm = row.bmbm;
      this.itemCodeDialog.dialogVisible = true;
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          departApi.remove({ id: row.id }).then((a) => {
            if (a.code == 200) {
              this.$message.success("删除成功");
              this.query.pageNo = 0;
              this.getData();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => {});
    },
    cancel() {
      this.form = {};
      this.ruleForm = {};
      this.dialog.dialogVisible = false;
      this.itemCodeDialog.dialogVisible = false;
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          departApi.save(this.ruleForm).then((a) => {
            if (a.code == 200) {
              this.$message.success("保存成功");
              this.query.pageNo = 0;
              this.cancel();
              this.getData();
            } else {
              this.$message.error("保存失败");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getData() {},
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.query.pageNoTemp = val;
      this.getData();
    },
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}
.searchDiv {
  margin-top: 10px;
}
</style>

<template>
 <div>
   <el-row :gutter="20" style="margin-top: 50px">
     <el-col :span="3">
       <div class="grid-content"></div>
     </el-col>
     <el-col :span="4">
       <el-tooltip placement="right">
         <div slot="content">统计部门或事项采集数据量</div>
         <i class="el-icon-question icon-color"></i>
       </el-tooltip>
       <span class="demonstration" style="margin-right: 10px">查询类型</span>

       <el-select v-model="queryForm.type" placeholder="请选择" @change="cleanTableData">
         <el-option
             v-for="item in options"
             :key="item.value"
             :label="item.label"
             :value="item.value">
         </el-option>
       </el-select>
     </el-col>
     <el-col :span="4" v-if="queryForm.type === `dept`">
       <span class="demonstration" style="margin-right: 10px">部门类型</span>
       <el-select v-model="queryForm.deptType" placeholder="请选择" @change="cleanTableData">
         <el-option
             v-for="item in deptTypes"
             :key="item.value"
             :label="item.label"
             :value="item.value">
         </el-option>
       </el-select>
     </el-col>
     <!--   查询条件  -->
     <el-col :span="4">
       <div>
         <span class="demonstration" style="margin-right: 10px">收件开始时间</span>
         <el-date-picker
             v-model="queryForm.startTime"
             type="datetime"
             value-format="yyyy-MM-dd HH:mm:ss"
             placeholder="选择日期时间">
         </el-date-picker>
       </div>
     </el-col>
     <el-col :span="4">
       <div>
         <span class="demonstration" style="margin-right: 10px">收件结束时间</span>
         <el-date-picker
             v-model="queryForm.endTime"
             type="datetime"
             value-format="yyyy-MM-dd HH:mm:ss"
             placeholder="选择日期时间">
         </el-date-picker>
       </div>
     </el-col>
     <el-col :span="5">
       <div>
         <el-button type="primary" @click="handleQuery">查询</el-button>
         <el-button type="warning" plain @click="handleExport">导出</el-button>
       </div>
     </el-col>
     <el-col :span="2" class="grid-content"></el-col>
   </el-row>
   <el-row :gutter="20">
     <el-col :span="20" :offset="2">
       <el-table
           stripe
           :data="tableData"
           height="800"
           style="width: 100%"
           header-cell-style="color:black;font-weight: 800;font-size:15px"
       >
         <el-table-column prop="taskCode" label="序号" align="center">
           <template slot-scope="scope">
             {{ scope.$index + 1 }}
           </template>
         </el-table-column>
         <el-table-column
             align="center"
             prop="bjDeptName"
             label="部门名称"
             width="180">
         </el-table-column>
<!--         <el-table-column
             align="center"
             prop="tongyicode"
             label="部门编码"
             width="180">
         </el-table-column>-->
         <el-table-column
             :key="Math.random()"
             v-if="this.queryForm.type != 'dept'"
             align="center"
             prop="taskName"
             label="事项名称"
             width="180">
         </el-table-column>
         <el-table-column
             :key="Math.random()"
             v-if="this.queryForm.type != 'dept'"
             align="center"
             label="事项编码"
             prop="taskCode"
             width="180">
         </el-table-column>

         <el-table-column
             v-if="this.queryForm.startTime != null"
             align="center"
             prop="startTime"
             label="收件开始时间">
         </el-table-column>
         <el-table-column
             v-="this.queryForm.endTime != null"
             align="center"
             prop="endTime"
             label="收件结束时间">
         </el-table-column>

         <el-table-column
             align="center"
             prop="collectingCount"
             label="采集总数">
         </el-table-column>
         <el-table-column
             align="center"
             prop="collecting"
             label="采集中">
         </el-table-column>

         <el-table-column
             align="center"
             prop="counted"
             label="已清点">
         </el-table-column>
         <el-table-column
             align="center"
             prop="qsFile"
             label="缺失材料">
         </el-table-column>
         <el-table-column
             align="center"
             prop="handOver"
             label="移交预归档">
         </el-table-column>

       </el-table>
     </el-col>
   </el-row>
 </div>
</template>
  <script>
  import {query,exportData} from '../api/collection'
    export default {
      data() {
        return {
          tableHeight:0,
          queryForm: {
            type: "dept"
          },
          options: [{
            value: 'dept',
            label: '部门'
          }, {
            value: 'itemCode',
            label: '事项'
          }],
          deptTypes: [
            {
              value: 'city',
              label: '市级'
            }, {
              value: 'town',
              label: '镇街'
            }],
          tableData: []
        }
      },
      mounted() {
        //获取容器当前高度，重设表格的最大高度
        this.getTableMaxHeight();
        let _this = this;
        window.onresize = function () {//用于使表格高度自适应的方法
          _this.getTableMaxHeight();//获取容器当前高度，重设表格的最大高度
        }
      },
      methods:{
        cleanTableData(){
          this.tableData  = []
        },
        handleQuery(){
          query(this.queryForm).then(res => {
            this.tableData = res.data
          })
        },
        handleExport(){
          this.$confirm("此操作将导出列表, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            exportData(this.queryForm).then(res=>{
              let url = window.URL.createObjectURL(new Blob([res]));
              let link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              if (this.queryForm.type == "dept"){
                link.setAttribute('download', '采集数据统计-部门.xlsx');
              }else {
                link.setAttribute('download', '采集数据统计-事项.xlsx');
              }

              document.body.appendChild(link);
              link.click();
            }).catch(e => {
              this.$message.error("导出失败");
            })
          })
        },
        //获取容器当前高度，重设表格的最大高度
        getTableMaxHeight(){
          this.$nextTick(function () {
            let box = document.querySelector(".content").attributes
            let box_clientHeight = box[0].ownerElement.clientHeight;
            this.tableHeight = box_clientHeight - 100;
          })
        },
      }
    }
</script>
<style scoped>
.el-row {
  margin-bottom: 20px;
&:last-child {
   margin-bottom: 0;
 }
}
.el-col {
  border-radius: 4px;
}
.bg-purple-dark {
  background: #99a9bf;
}
.bg-purple {
  background: #d3dce6;
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 4px;
  min-height: 36px;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
</style>
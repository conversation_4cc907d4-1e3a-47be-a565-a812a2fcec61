<template>
  <!-- 进度查询 -->
  <div class="box mote">
    <!-- <el-row :gutter="20">
      <el-col :span="7">
        <div class="down-tree">
          <TreeCommon @nodeClick="nodeClick"></TreeCommon>
        </div>
      </el-col>
      <el-col :span="17">
        <div class="searchDiv">
          <el-form
              :inline="true"
              :model="query"
              class="demo-form-inline"
              size="small"
          >
            <el-form-item :label="query.labelOne" v-if="query.type == 0">
              <el-input
                  v-model="query.bmbm"
                  placeholder="部门编码"
                  disabled
              ></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 0">
              <el-input
                  v-model="query.name"
                  placeholder="部门编码"
                  disabled
              ></el-input>
            </el-form-item>
            <el-form-item :label="query.labelOne" v-if="query.type == 1">
              <el-input
                  v-model="query.itemCode"
                  placeholder="部门编码"
                  disabled
              ></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 1">
              <el-input
                  v-model="query.itemName"
                  placeholder="部门编码"
                  disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="流水号">
              <el-input v-model="query.sblsh" placeholder="流水号"></el-input>
            </el-form-item>
            <el-form-item label="案卷号">
              <el-input
                  v-model="query.archivesNo"
                  placeholder="案卷号"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="readyFileContent">
          <el-row :gutter="16">
            <el-col :span="4" v-for="(item, index) in tableData" :key="index">
              <div
                  class="childContent"
                  :style="{ background: 'url(' + url + ')' }"
              >
                <div class="title">
                  {{ item.taskname }}
                </div>
                <div class="archivesNo">
                  {{ item.documentNumber }}
                </div>
                <div class="date">
                  {{ parseTime(item.createtime) }}
                </div>
                <div class="btn">
                  <el-button
                      size="small"
                      icon="el-icon-view"
                      @click="handleView(item)"
                  >浏览
                  </el-button
                  >
                  <el-button
                      size="small"
                      icon="el-icon-bottom"
                      @click="handleDownLoad(null, item)"
                  >下载
                  </el-button
                  >
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row> -->
    <el-card
      class="readyElCard"
      style="margin-top: 10px;
      padding: 0px 0px;
      width: 20%;
      height: 97%;
      overflow-y: scroll;
      overflow-x: scroll;"
        >
      <div class="down-tree">
          <TreeCommon :isFile="isFile" @nodeClick="nodeClick"></TreeCommon>
        </div>
      </el-card>
      <el-card class="readyFileCard">
        <div class="searchDiv">
          <el-form
              :inline="true"
              :model="query"
              class="demo-form-inline"
              size="small"
          >
            <el-form-item :label="query.labelOne" v-if="query.type == 0">
              <el-input
                  v-model="query.bmbm"
                  placeholder="部门编码"
                  disabled
              ></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 0">
              <el-input
                  v-model="query.name"
                  placeholder="部门编码"
                  disabled
              ></el-input>
            </el-form-item>
            <el-form-item :label="query.labelOne" v-if="query.type == 1">
              <el-input
                  v-model="query.itemCode"
                  placeholder="部门编码"
                  disabled
              ></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 1">
              <el-input
                  v-model="query.itemName"
                  placeholder="部门编码"
                  disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="流水号">
              <el-input v-model="query.sblsh" placeholder="流水号"></el-input>
            </el-form-item>
            <el-form-item label="案卷号">
              <el-input
                  v-model="query.archivesNo"
                  placeholder="案卷号"
              ></el-input>
            </el-form-item>
            <el-form-item label="移交状态">
              <el-select v-model="query.handOver" clearable placeholder="移交状态" style="width: 180px;">
                <el-option label="未移交" :value="0"></el-option>
                <el-option label="已移交" :value="1"></el-option>
                <el-option label="移交失败" :value="2"></el-option>
                <el-option label="正在移交" :value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
      <div class="ContentArea" id="ContentArea">
        <div class="readyFileContent">
          <el-row :gutter="16">
            <el-col :xs="{span:24,offset:0}" :sm="{span:8,offset:0}" :md="{span:4,offset:0}"
                    v-for="(item, index) in tableData" :key="index">

              <div
                  class="childContent article"
                  :style="{ background: 'url(' + url + ')' }"
              >
                <div class="flag flag-left" v-if="item.handover == 1">已移交</div>

                <div class="title">
                  {{ item.taskname }}
                </div>
                <div class="archivesNo">
                  {{ item.documentNumber }}
                </div>

                <div class="date">
                  {{ parseTime(item.createtime) }}
                </div>
                <div class="btn">
                  <el-button
                      size="small"
                      icon="el-icon-view"
                      @click="handleView(item)"
                  >浏览
                  </el-button
                  >
                  <el-button
                      size="small"
                      icon="el-icon-bottom"
                      @click="handleDownLoad(null, item)"
                  >下载
                  </el-button
                  >
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <el-pagination v-model:currentPage="query.pageSize" :page-sizes="[12, 24, 48, 96]"
      layout="total, sizes, prev, pager, next, jumper" :total="query.total" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" class="paginationStyle" />
     </div>
      </el-card>
    <el-dialog
        title="档案浏览"
        fullscreen
        :visible.sync="dialog.dialogVisible"
        :before-close="handleClose"
        center
        :append-to-body="true"
    >
      <FileView v-if="dialog.dialogVisible" :sblsh="dialog.sblsh"></FileView>

      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </span> -->
    </el-dialog>
  </div>
</template>
<script>
import TreeCommon from "../components/TreeCommon.vue";
import FileView from "../components/FileViewCommon.vue";
import Business from "../components/Business.vue";
import {itemBusinessApi} from "../api/itemBusiness";
import {fileApi} from "../api/file";
import {tool} from "../utils/tool";
import {materialsBillApi} from "../api/materialsBill";
import {logApi} from "../api/log";

export default {
  components: {
    TreeCommon,
    Business,
    FileView,
  },
  data() {
    return {
      url: require("../assets/img/backimg.jpg"),
      isFile: "2",
      baseForm: {},
      baseTableData: [],
      matterialTableData: [],
      treeData: [],
      logQuery: {
        pageNo: 0,
        pageSize: 12,
      },
      query: {
        pageNo: 0,
        pageSize: 12,
        total: 0,
        labelOne: "",
        labelTwo: "",
        type: null,
        isFile: 2,
        sblsh:"",
      },

      dialog: {
        title: "新增",
        dialogVisible: false,
        sblsh: "",
      },
      rules: {
        bmbm: [{required: true, message: "请输入部门编码", trigger: "blur"}],
        name: [{required: true, message: "请输入部门名称", trigger: "blur"}],
        qzh: [{required: true, message: "请输入全宗号", trigger: "blur"}],
      },
      ruleForm: {},
      tableData: [],
    };
  },
  created() {
    // this.getData();
  },
  methods: {
    parseTime(val) {
      return tool.formatDate(val);
    },
    //下载
    handleDownLoad(row, index) {
      let urlRequst = "";
      itemBusinessApi.getDownZipUrl({sblsh: index.sblsh}).then((a) => {
        if (a.code == 200 && a.msg) {
          fileApi.checkFile({address: a.msg}).then((c) => {
            if (c.code == 200) {
              if (process.env.NODE_ENV == "development") {
                urlRequst =
                    "http://localhost:8088/api/downloadApi/downFile?address=" +
                    a.msg;
              } else {
                urlRequst =
                    process.env.VUE_APP_FILE_BASE_API + "/downloadApi/downFile?address=" +
                    a.msg;
              }
              window.location.href = urlRequst;
            } else {
              this.$message.error("文件不存在");
            }
          });
        } else {
          this.$message.error("获取文件路径失败");
        }
      });
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    nodeClick(data) {
      console.log(data);
      this.query.pageNo = 0;
      this.query.sblsh = null;
      this.query.type = data.type;
      //部门
      if (data.type == 0) {
        this.query.bmbm = data.id;
        this.query.name = data.name;
        this.query.itemCode = null;
        this.query.itemName = null;
        this.query.labelOne = "部门编码";
        this.query.labelTwo = "部门名称";
      }
      //事项
      if (data.type == 1) {
        this.query.bmbm = null;
        this.query.name = null;
        this.query.itemCode = data.id;
        this.query.itemName = data.name;
        this.query.labelOne = "事项编码";
        this.query.labelTwo = "事项名称";
      }
      this.getData();
    },
    handleView(index, row) {
      this.dialog.title = "查看";
      this.ruleForm = row;
      this.dialog.sblsh = index.sblsh;
      this.dialog.dialogVisible = true;
      //   this.getBaseData(row.Sblsh);
    },
    handleClose(done) {
      this.form = {};
      done();
    },
    cancel() {
      this.form = {};
      this.dialog.dialogVisible = false;
    },
    getBaseData(val) {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "正在加载数据中...", //显示在加载图标下方的加载文案
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      Promise.all([
        new Promise((re, rp) => {
          itemBusinessApi.getItemSLEntity({sblsh: val}).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          logApi.page2(this.logQuery).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          itemBusinessApi.getFileBySblshAllNoPage(this.logQuery).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          materialsBillApi.getMaterialBillTree({sblsh: val}).then((a) => {
            re(a);
          });
        }),
      ])
          .then((a) => {
            this.baseForm = a[0].data;
            this.baseTableData = a[1].data.content;
            this.matterialTableData = a[2].data;
            this.treeData = a[3].data;
            this.dialog.dialogVisible = true;
            // this.pass=true;
            loading.close();
          })
          .catch((s) => {
            loading.close();
          });
    },
    getData() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text:"正在加载数据中...",
        target: document.querySelector("#ContentArea"), //loadin覆盖的dom元素节点
      });
      if (this.query.archivesNo == "") {
        this.query.archivesNo = null;
      }
      if (this.query.sblsh == "") {
        this.query.sblsh = null;
      }
      itemBusinessApi.getItemSlDataForMetaData(this.query).then((a) => {
        this.tableData = [];
        if (a.code == 200) {
          a.data.forEach((f) => {
            let businessStatus = 0;
            let tempName = "";
            if (f.name.lastIndexOf("年") > 0) {
              tempName = f.name.replaceAll("年", "");
            } else if (f.name.lastIndexOf("永久") > 0) {
              tempName = f.name.replaceAll("永久", "");
            }
            if (f.ArchivesNo) {
              f.ArchivesNo =
                  f.OrgCode +
                  "-" +
                  f.Localtaskcode +
                  "-" +
                  f.year +
                  "-" +
                  f.Type +
                  tempName +
                  "-" +
                  f.ArchivesNo;
            }
            if (f.itemSl) {
              businessStatus = 0;
            }
            if (f.itemGc) {
              businessStatus = 1;
            }
            if (f.itemBj) {
              businessStatus = 2;
            }
            if (f.itemLq) {
              businessStatus = 3;
            }
            f.businessStatus = businessStatus;
            this.tableData.push(f);
          });
          this.query.total = a.totalCount;
        }
        loading.close();
      });
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.query.pageNoTemp = val;
      this.getData();
    },
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}

.down-tree {
  height: 100%;
  display: inline-block;
   overflow-y: hidden;
}

.readyFileCard{
  width: 80%;
  margin-top: 10px;
  margin-left: 10px;
  display: flex;
}
.article {
  position: relative;
  overflow: hidden;
}
.article .flag {
  font-weight: 300;
  position: absolute;
  height: 20px;
  line-height: 20px;
  text-align: center;
  width: 100px;
  background-color: #FF5722;
  color: #fff;
}
.article .flag-left {
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  left: -25px;
  top: 15px;
}



.ContentArea {
  width: 100%;
  height: calc(100% - 100px);
  display: flow;
}

.searchDiv {
  margin-top: 10px;
}

.readyFileContent {
  width: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
  height: calc(100% - 50px);
   max-height: 800px;
}

.paginationStyle{
  position: absolute;
  height: 40px;
  right: 15px;
  bottom: 5px;
  margin-top: 10px;
}

.readyFileContent .childContent {
  width: 100%;
  height: 300px;
  margin-bottom: 20px;
  position: relative;
  text-align: center;
}

.readyFileContent .childContent:hover {
  cursor: pointer;
  box-shadow: 2px 2px 10px black;
}

.readyFileContent .childContent .btn {
  width: 100%;
  position: absolute;
  bottom: 10px;
}

.readyFileContent .childContent .date {
  width: 100%;
  position: absolute;
  bottom: 60px;
  height: 20px;
}

.readyFileContent .childContent .title {
  width: 100%;
  padding: 10px 0px;
  font-size: 20px;
  text-align: center;
}

.readyFileContent .childContent .archivesNo {
  width: 100%;
  padding: 10px 0px;
  font-size: 12px;
  text-align: center;
  word-wrap: break-word;
}

.mote {
  display: flex;
  height: 100%;
  box-sizing: border-box;
}

/deep/ .readyElCard .el-card__body{
  padding: 0 0 0 10px;
}
</style>

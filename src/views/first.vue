<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick" ref="tabs">
      <el-tab-pane label="申报基本信息" name="first">
        <BasicForm :form="BasicForm"></BasicForm>
      </el-tab-pane>
      <el-tab-pane label="审批过程信息" name="second">
        <div v-if="BzForm.length > 0">
          <div class="title">补正信息</div>
          <BzForm :form="BzForm"></BzForm>
        </div>
        <div v-if="TbcxForm.length > 0">
          <div class="title">特别程序信息</div>
          <TbcxForm :form="TbcxForm"></TbcxForm>
        </div>
        <div class="title">受理信息</div>
        <SLForm :form="SLForm"></SLForm>
        <div v-if="GCForm.length > 0">
          <div class="title">过程信息</div>
          <GCForm :form="GCForm"></GCForm>
        </div>
        <div class="title">办结信息</div>
        <BJForm :form="BJForm"/>
      </el-tab-pane>
      <el-tab-pane label="归档配置信息" name="third">
        <GDForm :form="GDForm"/>
      </el-tab-pane>
      <el-tab-pane label="电子文件信息" name="fourth">
        <!-- <el-row style="margin-top: 0px;margin-left: 5px">
         
          <el-col :span="5">
            <el-button size="small" type="primary" disabled v-if="!isFinite2" style="position: absolute;top: 0;">选取文件
            </el-button>
            <el-upload style="width: 100%" class="upload-demo" action="#" accept=".doc,.docx,.xls,.xlsx,.pdf,.png,.jpg"
              :before-remove="beforeRemove" :before-upload="beforeUpload" multiple :on-change="fileChange"
              :http-request="submitUpload" :file-list="fileList" :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary" v-if="isFinite2">选取文件</el-button>
              <el-button size="small" type="primary" disabled v-if="!isFinite2">选取文件</el-button>


              <div slot="tip" class="el-upload__tip">
                <div><span style="{font-weight:500;color: red;}">支持格式：.doc .docx .xls .xlsx .pdf .png jpg
                    单个文件不能超过5MB</span></div>
                <div><span style="{font-weight:500;color: red;}">提示：上传文件后会自动处理，如需查看请稍后操作</span></div>
              </div>
            </el-upload>
            <el-button class="san2" style="margin-left: 10px" size="small" type="success"
              @click="submitUpload">上传材料</el-button>
            <el-button v-if="isFinite1" class="san" style="margin-left: 10px" size="small" type="danger"
              @click="handleRemove">
              删除材料</el-button>
            <div style="padding: 0 5px" class="dic">
              <el-tree :data="data" :props="defaultProps" show-checkbox check-on-click-node node-key="fileName"
                :check-strictly="true" ref="tree" @check-change="" @node-click="handleNodeClick"
                @check="handleCheckboxClick" :default-expand-all="true">
              </el-tree>
            </div>
          </el-col>
          <el-col :span="19">
            <iframe :src="imgfs" frameborder="0" style="height: 90vh; width: 100%;margin-top: 50px;"></iframe>
          </el-col>
        </el-row> -->
        <div class="mote">
          <el-card style="
        margin-top: 50px;
        padding: 0px 10px;
        width: 20%;
        height: 100%;
        overflow-y: scroll;
      ">
            <!--            <el-button size="small" type="primary" disabled v-if="!isFinite2" style="position: absolute;top: 2px; z-index: 1;left: 10px;">选取文件-->
            <!--            </el-button>-->
            <el-upload style="width: 100%" ref="upload" class="upload-demo" action="#"
                       accept=".doc,.docx,.xls,.xlsx,.pdf,.png,.jpg"
                       :before-remove="beforeRemove" :before-upload="beforeUpload" multiple :on-change="fileChange"
                       :http-request="submitUpload" :file-list="fileList" :auto-upload="false">

              <div slot="tip" class="el-upload__tip">
                <div><span style="{font-weight:500;color: red;}">支持格式：.doc .docx .xls .xlsx .pdf .png jpg
                    单个文件不能超过5MB</span></div>
                <div><span style="{font-weight:500;color: red;}">提示：上传文件后会自动处理，如需查看请稍后操作</span>
                </div>
              </div>
            </el-upload>
            <el-button size="small" type="primary" v-if="isFinite2" class="san3" @click="selectFile">选取文件
            </el-button>
            <el-button size="small" type="primary" disabled v-if="!isFinite2" class="san3" @click="selectFile">
              选取文件
            </el-button>
            <el-button class="san2" style="margin-left: 10px" size="small" type="success"
                       @click="submitUpload">上传材料
            </el-button>
            <el-button v-if="isFinite1" class="san" style="margin-left: 10px" size="small" type="danger"
                       @click="handleRemove">
              删除材料
            </el-button>
            <div style="padding: 0 5px" class="dic">
              <el-tree :data="data" :props="defaultProps" show-checkbox check-on-click-node node-key="fileName"
                       :check-strictly="true" ref="tree" @check-change="" @node-click="handleNodeClick"
                       @check="handleCheckboxClick" :default-expand-all="true">
              </el-tree>
            </div>
          </el-card>
          <el-card style="width: 80%; margin-top: 50px; margin-left: 10px;height: 100%;">
            <iframe :src="imgfs" frameborder="0" style="height: 81vh; width: 100%;margin-top: 0px;"></iframe>
          </el-card>
        </div>

      </el-tab-pane>
    </el-tabs>
    <span slot="footer" class="dialog-footer">
<!--      <el-button type="primary" @click="save"-->
      <!--                 v-if="this.activeName != `fourth` & this.activeName != `third`">保 存</el-button>-->
      <el-button @click="tui">返 回</el-button>
      <el-button @click="tui" class="ti"><<-返 回</el-button>
    </span>
  </div>
</template>
<script>
import BasicForm from "../components/BasicForm.vue";
import SLForm from "../components/SLForm.vue";
import GCForm from "../components/GCForm.vue";
import BJForm from "../components/BJForm.vue";
import LQForm from "../components/LQForm.vue";
import GDForm from "@/components/GDForm.vue";
import {
  getBasicData,
  getBzData,
  getGcData,
  getTbcxData,
  getBjData,
  getSLData,
  getGdData,
  getAllFilePaths,
  removeFile,
  uploadFile,
  getUploadFileInfo, saveSbjcData, saveItemCodeData, saveItemSlData, saveItemGcData, saveItemBjData
} from "../api/first";
import {
  dictComputed,
  isTown,
  isAgent,
  cardTypeDict,
  applyType,
  applyinstSource,
  projectType,
  resultCetrType
} from "@/dict/dictionaries";
import BzForm from "@/components/BzForm";
import TbcxForm from "@/components/TbcxForm";

export default {
  components: {
    BzForm,
    BasicForm,
    GCForm,
    BJForm,
    LQForm,
    GDForm,
    SLForm,
    TbcxForm
  },
  data() {
    return {
      activeName: "first",
      BasicForm: {},
      BzForm: [],
      TbcxForm: [],
      SLForm: {},
      GCForm: [],
      BJForm: {},
      GDForm: {},
      fileData: "",
      fresh: true,
      data: [],
      imgfs: "",
      defaultProps: {
        children: "listFiles",
        label: "fileName",
      },
      parameterMajor_id: [],
      fileName: "",
      fileList: [],
      setDeleteids: [],
      files: {},
      tree: [],
      input2: '',
      isFinite2: false,
      isFinite1: false,
      fileType: 0
    };
  },
  created() {
    this.getTree();
    this.getData();
  },
  methods: {
    formatDate(value) {
      var date = new Date(parseInt((value)));
      var y = date.getFullYear(),
          m = date.getMonth() + 1,
          d = date.getDate(),
          h = date.getHours(),
          i = date.getMinutes(),
          s = date.getSeconds();
      if (m < 10) {
        m = '0' + m;
      }
      if (d < 10) {
        d = '0' + d;
      }
      if (h < 10) {
        h = '0' + h;
      }
      if (i < 10) {
        i = '0' + i;
      }
      if (s < 10) {
        s = '0' + s;
      }
      var t = y + '-' + m + '-' + d + ' ' + h + ':' + i + ':' + s;
      return t;
    },
    getData() {
      getBasicData({sblsh: localStorage.getItem("sblsh"), name: "basic"}).then(
          (res) => {
            if (res.code) {
              this.BasicForm = res.data;
              this.BasicForm.isAgent = dictComputed(isAgent, res.data.isAgent).get();
              this.BasicForm.bsCardType = dictComputed(cardTypeDict, res.data.bsCardType).get();
              this.BasicForm.jCardType = dictComputed(cardTypeDict, res.data.jCardType).get();
              this.BasicForm.agentCardType = dictComputed(cardTypeDict, res.data.agentCardType).get();
              this.BasicForm.applyType = dictComputed(applyType, res.data.applyType).get();
              this.BasicForm.applyinstSource = dictComputed(applyinstSource, res.data.applyinstSource).get();
            }
          }
      );
      getBzData({sblsh: localStorage.getItem("sblsh"), name: "gc"}).then((res) => {
        this.BzForm = res.data;
      });
      getSLData({sblsh: localStorage.getItem("sblsh"), name: "gc"}).then(
          (res) => {
            if (res.code == 200) {
              this.SLForm = res.data;
              this.SLForm.applydate = this.formatDate(res.data.applydate)
              this.SLForm.acceptdate = this.formatDate(res.data.acceptdate)
              this.SLForm.hasTown = dictComputed(isTown, res.data.hasTown).get();
              this.SLForm.projecttype = dictComputed(projectType, res.data.projecttype).get();

            }
          }
      );
      getTbcxData({sblsh: localStorage.getItem("sblsh"), name: "gc"}).then(
          (res) => {
            if (res.code == 200) {
              res.data.forEach(item => {
                item.specialtime = this.formatDate(item.specialtime)
                item.endtime = this.formatDate(item.endtime)
              })
              this.TbcxForm = res.data;
            }
          }
      );
      getGcData({sblsh: localStorage.getItem("sblsh"), name: "gc"}).then(
          (res) => {
            if (res.code == 200) {
              res.data.forEach(item => {
                item.eventstarttime = this.formatDate(item.eventstarttime)
                item.eventEndTime = this.formatDate(item.eventEndTime)
              })
              this.GCForm = res.data;
            }
          }
      );
      getBjData({sblsh: localStorage.getItem("sblsh")}).then((res) => {
        if (res.code == 200) {
          this.BJForm = res.data;
          this.BJForm.resultdate = this.formatDate(res.data.resultdate)
          this.BJForm.hasTown = dictComputed(isTown, res.data.hasTown).get();
          this.BJForm.resultCetrType = dictComputed(resultCetrType, res.data.resultCetrType).get();
        }
      });
      getGdData({sblsh: localStorage.getItem("sblsh"), name: 'pz'}).then((res) => {
        if (res.code == 200) {
          this.GDForm = res.data;
          this.GDForm.className = res.data.classificationType.name
          this.GDForm.retenName = res.data.retentionPeriod.name
          this.GDForm.year = res.data.year.substr(0, 4)

          this.GDForm.tiMing = this.BJForm.tiMing;
          this.GDForm.wenHao = this.BJForm.wenHao;
        }
      });
      getUploadFileInfo({sblsh: localStorage.getItem("sblsh")}).then(res => {
        if (res.code === 200) {
          if (res.data !== null) {
            res.data.forEach(a => {
              this.$notify({
                title: '警告',
                message: a.fileName + a.content,
                type: 'warning'
              });
            })
          }
        }
      });
    },

    handleClick(tab, event) {
      switch (tab.name) {
        case "second":
          this.activeName = tab.name
          // this.$router.push({
          //   path: "./second",
          //   query: { sblsh: localStorage.getItem("sblsh") },
          // });
          break
        case "third":
          this.activeName = tab.name
          // this.$router.push({
          //   path: "./third",
          //   query: { sblsh: localStorage.getItem("sblsh") },
          // });
          break;
        case "fourth":
          this.activeName = tab.name
          // this.$router.push({
          //   path: "./fourth",
          //   query: { sblsh: localStorage.getItem("sblsh") },
          // });
          break;
        case "first":
          this.activeName = tab.name
          // this.$router.push({
          //   path: "./first",
          //   query: { sblsh: localStorage.getItem("sblsh") },
          // });
          // this.getData()
          break;
          // case "return":
          //   // this.activeName = tab.name
          //   // this.fresh = false
          //   this.activeName = 'first'
          //   this.$router.push({
          //     path: "./metadataManagement",
          //     // query: { sblsh: localStorage.getItem("sblsh") },
          //   });

          // break;
      }
    },
    action() {
      return process.env.VUE_APP_FILE_BASE_API + "/uploadApi/uploadFile"
    },
    getTree() {
      getAllFilePaths({sblsh: localStorage.getItem("sblsh")}).then((res) => {
        if (res.code == 200) {
          this.data = res.data;
        }
      });
    },
    handleNodeClick(data) {
      this.$refs.tree.setCheckedNodes([data]);
      let id = this.$refs.tree.getCheckedKeys();
      // console.log(data,9999)
      if (data.fileName.indexOf('pdf') !== -1) {
        this.isFinite1 = true
        this.isFinite2 = false
      } else {
        this.isFinite2 = true
        this.isFinite1 = false
      }
      this.tree = id;
      this.files = data;
      if (id instanceof Array && id[0] === data.fileName) {
        this.handleCheckChange(data, false);
      } else {
        this.handleCheckChange(data, true);
      }
      if (data.fileName.indexOf(".") !== -1) {
        this.imgfs = process.env.VUE_APP_FILE_API + "/" + data.path;
      }
    },
    //点击复选框时
    handleCheckboxClick(data, obj) {
      this.handleNodeClick(data)
      this.$refs.tree.setCheckedKeys(data);
    },
    handleCheckChange(data, checked) {
      const _this = this;
      if (checked) {
        this.fileName = data.fileName;
        this.$refs.tree.setCheckedKeys([data.fileName]);
      } else {
        if (this.fileName === data.fileName) {
          this.parameterMajor_id = [];
          this.fileName = "";
          this.$refs.tree.setCheckedKeys([]);
        }
      }
    },
    handleRemove() {
      var arry = this.$refs.tree.getCheckedNodes(true);
      let filePath = [];
      arry.forEach(v => {
        filePath.push(v.path);
      })
      var formData = new FormData();
      formData.set("path", filePath);
      formData.set("sblsh", localStorage.getItem("sblsh"))
      this.$confirm("此操作为材料删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        removeFile(formData).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.getTree();
          } else {
            this.$message({
              type: 'warning',
              message: res.msg
            })
          }
        })
      }).catch()
    },
    beforeRemove(file, fileList) {
      // console.log(11)
      let index = this.fileList.findIndex(
          (fileItem) => fileItem.uid === file.uid
      );
      this.fileList.splice(index, 1);
    },
    fileChange(file, fileList) {
      this.fileList = fileList;
      let fileName = file.name;
      let uid = file.uid
      let pos = fileName.lastIndexOf(".");
      let lastName = fileName.substring(pos, fileName.length);
      if (
          lastName.toLowerCase() !== ".doc" &&
          lastName.toLowerCase() !== ".docx" &&
          lastName.toLowerCase() !== ".xls" &&
          lastName.toLowerCase() !== ".xlsx" &&
          lastName.toLowerCase() !== ".pdf" &&
          lastName.toLowerCase() !== ".png" &&
          lastName.toLowerCase() !== ".jpg"
      ) {
        this.$message.error("文件必须为.doc .docx .xls .xlsx .pdf .png jpg 类型");
        // this.resetCompressData()
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].uid == uid) {
            fileList.splice(i, 1)
          }
        }
        this.fileList = [];
        return;
      }
      // 限制上传文件的大小
      const isLt =
          file.size / 1024 / 0 >= 1 && file.size / 1024 / 1024 / 5 <= 1;
      if (!isLt) {
        this.$message.error("上传文件大小不得小于0KB,不得大于5MB!");
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].uid == uid) {
            fileList.splice(i, 1)
          }
        }
        this.fileList = [];
      }
      let existFile = fileList.slice(0, fileList.length - 1).find(f => f.name === file.name)//如果文件名重复
      if (existFile) {
        this.$message.error('当前文件已经存在!');
        fileList.pop()
      }
      this.fileList = fileList
    },
    beforeUpload(file, fileList) {
      return isLt;
    },
    selectFile() {
      this.$confirm("上传文件是否为 数字化件?", "提示", {
        confirmButtonText: "是",
        cancelButtonText: "否",
        type: "warning",
      }).then(() => {
        this.fileType = 1
      }).catch(() => {
        this.fileType = 0
      }).finally(() => {
        this.$refs.upload.$refs['upload-inner'].handleClick()
      })
    },
    submitUpload() {
      if (this.tree.length > 0 && this.files.fileName.indexOf(".") === -1) {
        let formData = new FormData();
        formData.append("path", this.files.path);
        formData.append("file", this.fileList);
        formData.append("sblsh", localStorage.getItem("sblsh"));
        this.fileList.forEach((items) => {
          formData.append("file", items.raw);
        });
        formData.append("fileType", this.fileType)
        this.getUploadFile(formData);
      } else {
        this.$message({
          message: "请选择目录",
          type: "warning",
        });
      }
    },
    getUploadFile(data) {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector(".el-tabs__content"), //loadin覆盖的dom元素节点
      });
      uploadFile(data).then((res) => {
        loading.close()
        if (res.code !== 200) {
          this.$message({
            message: res.msg,
            type: "error",
          });
        } else {
          this.$message({
            message: res.msg,
            type: "success",
          });
        }
        this.fileList = [];
        setTimeout(() => {
          this.getTree();
        }, 30 * 1000)
      });

    },
    save() {
      switch (this.activeName) {
        case "first":
          if (this.BasicForm !== null) {
            saveSbjcData(this.BasicForm).then(res => {
              if (res.code == 200) {
                this.$message.success(res.msg)
              } else {
                this.$message.error(res.msg)
              }
            })
          }
          break
        case "second":
          if (this.SLForm !== null) {
            saveItemSlData(this.SLForm)
          }
          if (this.GCForm !== null) {
            saveItemGcData(this.GCForm)
          }
          if (this.BJForm !== null) {
            saveItemBjData(this.BJForm).then(res => {
              if (res.code == 200) {
                this.$message.success(res.msg)
              } else {
                this.$message.error(res.msg)
              }
            })
          }
          break
        case "third":
          saveItemCodeData(this.BasicForm).then(res => {
            if (res.code == 200) {
              this.$message.success(res.msg)
            } else {
              this.$message.error(res.msg)
            }
          })
          break;
        case "fourth":
          break;
      }
    },
    cancel() {
    },
    tui() {
      this.activeName = 'first'
      this.$router.push({path: "./metadataManagement"});
      // this.$router.back()
    },
    // watchInputNameFunc(newName, oldName){
    //         console.log(newName) // this.inputValue的值
    //         if(newName != 'first'){
    //           // debugger
    //           // window.location.reload()
    //           // this.fresh = true
    //           this.activeName =newName
    //           // this.$refs.tabs.refreshs()
    //           this.getData()
    //         }
    //     },
  },
  // watch: {
  //   activeName :'watchInputNameFunc'
  // },

};
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  padding-bottom: 80px;
}

::v-deep #tab-return {
  /* margin-left: 100%; */
  /* border: #3a8ee6 solid 1px; */
  float: right;
  right: -230%;
  padding: 5px 15px;
  align-content: center;
  margin-top: 2px;
  border-radius: 5px;
  text-align: center;
  line-height: 20px;
  font-size: medium;
}

.title {
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
  padding-bottom: 10px;
  font-size: 16px;
  margin-left: 5px;
  font-weight: bolder;
}

.text {
  font-size: 18px;
  margin-left: 60px;
  color: red;
}

/deep/ .el-tree-node__label {
  font-size: 15px;
  /* padding: 3px 0; */
}

/deep/ .el-tree-node {
  padding: 3px 0;
}

/deep/ .el-card__body {
  padding: 0;
}

.el-upload__tip {
  font-size: 13px;
}

.san {
  position: absolute;
  top: 2px;
  left: 180px;
}

.san2 {
  position: absolute;
  top: 2px;
  left: 90px;
}

.san3 {
  position: absolute;
  top: 2px;
  left: 10px;
  /* left: 108px; */
}

.dic {
  overflow-x: scroll;
  height: 72vh;
}


.ti {
  position: absolute;
  top: 0px;
  border: 0;
  background-color: rgba(0, 0, 0, 0);
  right: 10px;
}

.mote {
  display: flex;
  height: 100%;
  box-sizing: border-box;
  padding: 0 10px;
}

</style>

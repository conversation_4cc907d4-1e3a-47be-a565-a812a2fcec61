<template>
  <!-- 进度查询 -->
  <div class="mote">
    <!-- <el-row :gutter="20" style="margin-left: 0px; margin-right: 0px; padding: 0px">
      <el-col :span="6">
        <div class="down-tree" ref="downTree">
          <TreeCommon @nodeClick="nodeClick"></TreeCommon>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="searchDiv">
          <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
            <el-form-item :label="query.labelOne" v-if="query.type == 0">
              <el-input v-model="query.bmbm" placeholder="部门编码" disabled></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 0">
              <el-input v-model="query.name" placeholder="部门编码" disabled></el-input>
            </el-form-item>
            <el-form-item :label="query.labelOne" v-if="query.type == 1">
              <el-input v-model="query.itemCode" placeholder="部门编码" disabled></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 1">
              <el-input v-model="query.itemName" placeholder="部门编码" disabled></el-input>
            </el-form-item>
            <el-form-item label="流水号">
              <el-input v-model="query.sblsh" placeholder="流水号"></el-input>
            </el-form-item>
            <el-form-item label="档案号">
              <el-input v-model="query.archivesNo" placeholder="档案号"></el-input>
            </el-form-item>
            <el-form-item label="一体化状态">
              <el-select v-model="query.status" placeholder="请选择状态">
                <el-option label="全部" value="" checked></el-option>
                <el-option label="收件" value="0"></el-option>
                <el-option label="受理" value="1"></el-option>
                <el-option label="审批" value="2"></el-option>
                <el-option label="办结" value="3"></el-option>
                <el-option label="出证" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="tableData" class="tableClass" :height="tableHeight" border>
          <el-table-column prop="taskCode" label="序号" width="50" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="sblsh" label="流水号" align="center"></el-table-column>
          <el-table-column prop="archivesNo" label="档案号" align="center"></el-table-column>
          <el-table-column prop="documentNumber" label="电子文件号" align="center"></el-table-column>
          <el-table-column prop="contactcode" label="证件号" align="center"></el-table-column>
          <el-table-column prop="businessStatus" label="一体化状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 0">收件</span>
              <span v-if="scope.row.status == 1">受理</span>
              <span v-else-if="scope.row.status == 2">审批</span>
              <span v-else-if="scope.row.status == 3">办结</span>
              <span v-else-if="scope.row.status == 4">出证</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isfile == 1">已采集</div>
              <div v-else-if="scope.row.isfile == 2">已同步</div>
              <div v-else-if="scope.row.isfile == 3">已送达</div>
            </template>
          </el-table-column>
          <el-table-column prop="contactname" label="联系人" align="center"></el-table-column>

          <el-table-column prop="createtime" label="创建时间" align="center"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-view" @click="handleView(scope.$index, scope.row)">查看
              </el-button>
              <el-button type="text" icon="el-icon-download" :disabled="scope.row.isfile == 1 || scope.row.isfile == 0"
                @click="handleDownLoad(scope.$index, scope.row)">下载
              </el-button>
             
            </template>
          </el-table-column>
        </el-table>
    
        <el-pagination v-model:currentPage="currentPage4" v-model:page-size="pageSize4" :page-sizes="[10, 20, 50, 100]"
          :small="small" :disabled="disabled" :background="background" layout="total, sizes, prev, pager, next, jumper"
          :total="query.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </el-col>
    </el-row> -->
    <el-card 
        class="progressTree"
        style="
        margin-top: 10px;
        padding: 0px 0px;
        width: 20%;
        height: 97%;
        overflow-y: scroll;
      ">
      <div class="down-tree" ref="downTree">
        <TreeCommon @nodeClick="nodeClick"></TreeCommon>
      </div>
    </el-card>
    <el-card style="width: 80%; margin-top: 10px; margin-left: 10px;height: 97%;">
      <div class="searchDiv1">
        <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
          <el-form-item :label="query.labelOne" v-if="query.type == 0">
            <el-input v-model="query.bmbm" placeholder="部门编码" disabled></el-input>
          </el-form-item>
          <el-form-item :label="query.labelTwo" v-if="query.type == 0">
            <el-input v-model="query.name" placeholder="部门编码" disabled></el-input>
          </el-form-item>
          <el-form-item :label="query.labelOne" v-if="query.type == 1">
            <el-input v-model="query.itemCode" placeholder="部门编码" disabled></el-input>
          </el-form-item>
          <el-form-item :label="query.labelTwo" v-if="query.type == 1">
            <el-input v-model="query.itemName" placeholder="部门编码" disabled></el-input>
          </el-form-item>
          <el-form-item label="流水号">
            <el-input v-model="query.sblsh" placeholder="流水号"></el-input>
          </el-form-item>
          <el-form-item label="档案号">
            <el-input v-model="query.archivesNo" placeholder="档案号"></el-input>
          </el-form-item>
          <el-form-item label="一体化状态">
            <el-select v-model="query.status" placeholder="请选择状态">
              <el-option label="全部" value="" checked></el-option>
              <el-option label="收件" value="0"></el-option>
              <el-option label="受理" value="1"></el-option>
              <el-option label="审批" value="2"></el-option>
              <el-option label="办结" value="3"></el-option>
              <el-option label="出证" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table id="tableData" :data="tableData" class="tableClass" :height="tableHeight"
                :header-cell-style="{ background: '#F6F7FA' }">
        <el-table-column prop="taskCode" label="序号" width="50" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column width="200" prop="sblsh" label="流水号" align="center"></el-table-column>
        <el-table-column prop="contactName" label="联系人" align="center"></el-table-column>
        <el-table-column width="200" prop="contactCode" label="证件号" align="center"></el-table-column>
        <el-table-column width="450" prop="documentNumber" label="电子文件号" align="center"></el-table-column>
        <el-table-column prop="businessStatus" label="一体化状态" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 0">收件</span>
            <span v-if="scope.row.status == 1">受理</span>
            <span v-else-if="scope.row.status == 2">审批</span>
            <span v-else-if="scope.row.status == 3">办结</span>
            <span v-else-if="scope.row.status == 4">出证</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.isFile == 1">已采集</div>
            <div v-else-if="scope.row.isFile == 2">已同步</div>
            <div v-else-if="scope.row.isFile == 3">已送达</div>
          </template>
        </el-table-column>

        <el-table-column width="200" prop="createTime" label="创建时间" align="center"></el-table-column>
        <el-table-column prop="archivesNo" label="档案号" align="center"></el-table-column>
        <el-table-column fixed="right" label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-view" @click="handleView(scope.$index, scope.row)">查看
            </el-button>
            <el-button type="text" icon="el-icon-download" :disabled="scope.row.isFile == 1 || scope.row.isFile == 0"
              @click="handleDownLoad(scope.$index, scope.row)">下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:page-size="query.pageSize" :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper" :total="query.total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange"  style="float: right;margin-top: 10px;"/>
    </el-card>
    <el-dialog :title="dialog.title" fullscreen :visible.sync="dialog.dialogVisible" :before-close="handleClose" center
      :append-to-body="true">
      <div style="height: 100vh; overflow: auto">
        <Business v-if="dialog.dialogVisible" :sblsh="ruleForm.sblsh"></Business>
      </div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </span> -->
    </el-dialog>
  </div>
</template>
<script>
import TreeCommon from "../components/TreeCommon.vue";
import Business from "../components/Business.vue";
import { itemBusinessApi } from "../api/itemBusiness";
import { fileApi } from "../api/file";
import { tool } from "../utils/tool";
import { materialsBillApi } from "../api/materialsBill";
import { logApi } from "../api/log";

export default {
  components: {
    TreeCommon,
    Business,
  },
  data() {
    return {
      tableHeight: 730,
      baseForm: {},
      baseTableData: [],
      matterialTableData: [],
      treeData: [],
      logQuery: {
        pageNo: 0,
        pageSize: 10,
      },
      query: {
        pageNo: 0,
        pageSize: 10,
        total: 0,
        labelOne: "",
        labelTwo: "",
        type: null,
        sblsh: "",
        archivesNo: "",
        status: ""
      },

      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      rules: {
        bmbm: [{ required: true, message: "请输入部门编码", trigger: "blur" }],
        name: [{ required: true, message: "请输入部门名称", trigger: "blur" }],
        qzh: [{ required: true, message: "请输入全宗号", trigger: "blur" }],
      },
      ruleForm: {},
      tableData: [],
    };
  },
  created() {
  },
  mounted() {
    this.initTableHeight();
  },
  methods: {
    handleTransferZip(idnex, row) {
      this.$confirm("此操作为Zip包移交, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const loading = this.$loading({
            lock: true, //lock的修改符--默认是false
            text:"正在加载数据中...",
            target: document.querySelector("#tableData"), //loadin覆盖的dom元素节点
          });

          itemBusinessApi
            .transferZip({ sblsh: row })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.msg);
                this.getData();
              } else {
                this.$message.error(res.msg);
              }
              loading.close();
            })
            .catch(() => {
              this.$message.error("移交失败,系统异常");
              loading.close();
            });
        })
        .catch(() => {
        });
    },
    parseTime(val) {
      if (!val) {
        return "";
      }
      let time = parseInt(val);
      return tool.parseTime(time);
    },
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      let that = this;
      // 一定要使用 nextTick 来改变height 不然不会起作用
      //nav 45;
      //padding:10
      this.$nextTick(() => {
        let tempHeight = height - 45 - 100 - 51;
        let treeDemoHeight = tempHeight + 30;
        that.$refs.downTree.style = "height:" + treeDemoHeight + "px";
        console.log(that.$refs.downTree.style);

        this.tableHeight = tempHeight;
        console.log(this.tableHeight, "height");
      });
    },
    //下载
    handleDownLoad(row, index) {
      let urlRequst = "";
      itemBusinessApi.getDownZipUrl({ sblsh: index.sblsh }).then((a) => {
        if (a.code == 200 && a.msg) {
          fileApi.checkFile({ address: a.msg }).then((c) => {
            if (c.code == 200) {
              if (process.env.NODE_ENV == "development") {
                urlRequst =
                  "http://localhost:8088/api/downloadApi/downFile?address=" +
                  a.msg;
              } else {
                urlRequst =
                    process.env.VUE_APP_FILE_BASE_API + "/downloadApi/downFile?address=" +
                  a.msg;
              }
              window.location.href = urlRequst;
            } else {
              this.$message.error("文件不存在");
            }
          });
        } else {
          this.$message.error("获取文件路径失败");
        }
      });
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    nodeClick(data) {
      console.log(data);
      this.query.pageNo = 0;
      this.query.sblsh = null;
      this.query.type = data.type;
      //部门
      if (data.type == 0) {
        this.query.bmbm = data.id;
        this.query.name = data.name;
        this.query.itemCode = null;
        this.query.itemName = null;
        this.query.labelOne = "部门编码";
        this.query.labelTwo = "部门名称";
      }
      //事项
      if (data.type == 1) {
        this.query.bmbm = null;
        this.query.name = null;
        this.query.itemCode = data.id;
        this.query.itemName = data.name;
        this.query.labelOne = "事项编码";
        this.query.labelTwo = "事项名称";
      }
      this.getData();
    },
    handleView(index, row) {
      this.dialog.title = "查看";
      this.ruleForm = row;
      this.logQuery.sblsh = row.sblsh;
      this.dialog.dialogVisible = true;
      //   this.getBaseData(row.Sblsh);
    },
    handleClose(done) {
      this.form = {};
      done();
    },
    cancel() {
      this.form = {};
      this.dialog.dialogVisible = false;
    },
    getBaseData(val) {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text:"正在加载数据中...",
        fullscreen:true
      });
      Promise.all([
        new Promise((re, rp) => {
          itemBusinessApi.getItemSLEntity({ sblsh: val }).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          logApi.page2(this.logQuery).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          itemBusinessApi.getFileBySblshAllNoPage(this.logQuery).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          materialsBillApi.getMaterialBillTree({ sblsh: val }).then((a) => {
            re(a);
          });
        }),
      ])
        .then((a) => {
          this.baseForm = a[0].data;
          this.baseTableData = a[1].data.content;
          this.matterialTableData = a[2].data;
          this.treeData = a[3].data;
          this.dialog.dialogVisible = true;
          // this.pass=true;
          loading.close();
        })
        .catch((s) => {
          loading.close();
        });
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          itemBusinessApi.save(this.ruleForm).then((a) => {
            if (a.code == 200) {
              this.$message.success("保存成功");
              this.query.pageNo = 0;
              this.cancel();
              this.getData();
            } else {
              this.$message.error("保存失败");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getData() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text:"正在加载数据中...",
        target: document.querySelector("#tableData"), //loadin覆盖的dom元素节点
      });
      if (this.query.archivesNo == "") {
        this.query.archivesNo = null;
      }
      if (this.query.sblsh == "") {
        this.query.sblsh = null;
      }
      itemBusinessApi.page(this.query).then((a) => {
        this.tableData = [];
        if (a.code == 200) {
          a.data.forEach((f) => {
            let businessStatus = 0;
            let tempName = "";
            if (f.name.lastIndexOf("年") > 0) {
              tempName = f.name.replaceAll("年", "");
            } else if (f.name.lastIndexOf("永久") > 0) {
              tempName = f.name.replaceAll("永久", "");
            }
            if (f.itemSl) {
              businessStatus = 0;
            }
            if (f.itemGc) {
              businessStatus = 1;
            }
            if (f.itemBj) {
              businessStatus = 2;
            }
            if (f.itemLq) {
              businessStatus = 3;
            }
            f.businessStatus = businessStatus;
            this.tableData.push(f);
          });
          this.query.total = a.totalCount;
        }
        loading.close()
      });
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.query.pageNoTemp = val;
      this.getData();
    },
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}

.down-tree {
  height: 700px;
  display: block;
  /* overflow-y: scroll; */
}

.searchDiv {
  margin-top: 10px;
}

.mote {
  display: flex;
  height: 100%;
  box-sizing: border-box;
}

/deep/ .progressTree .el-card__body{
  padding: 0 0 0 10px;
}
</style>

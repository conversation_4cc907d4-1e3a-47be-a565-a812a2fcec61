<template>
  <div class="fileDiv">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="first"> </el-tab-pane>
      <el-tab-pane label="流程信息" name="second"> </el-tab-pane>
      <el-tab-pane label="归档信息" name="third"> </el-tab-pane>
      <el-tab-pane label="电子文件信息" name="fourth">
        
        <el-row style="margin-top: 0px;margin-left: 5px">
          <!--          accept=".doc,.docx,.xls,.xlsx,.pdf,.png,.jpg"-->
          <el-col :span="5">
<!--            <el-button  size="small" type="primary" disabled v-if="!isFinite2" style="position: absolute;top: 0;">选取文件</el-button>
            <el-upload style="width: 100%" class="upload-demo" action="#" accept=".doc,.docx,.xls,.xlsx,.pdf,.png,.jpg"
              :before-remove="beforeRemove" :before-upload="beforeUpload" multiple :on-change="fileChange"
              :http-request="submitUpload" :file-list="fileList" :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary" v-if="isFinite2">选取文件</el-button>
              <el-button  size="small" type="primary" disabled v-if="!isFinite2">选取文件</el-button>
              <el-button style="margin-left: 10px" size="small" type="success" @click="submitUpload" >上传材料</el-button>
              <el-button style="margin-left: 10px" size="small" type="danger" @click="handleRemove" v-if="isFinite1">删除材料</el-button>
              <div slot="tip" class="el-upload__tip">
                支持格式：
                <div>.doc .docx .xls .xlsx .pdf .png jpg 单个文件不能超过5MB</div>
              </div>
            </el-upload>-->
            <div style="padding: 0 5px" class="dic">
              <!--          <el-input
                          placeholder="请输入内容"
                          prefix-icon="el-icon-search"
                          v-model="input2"
                        >
                        </el-input>-->
              <el-tree :data="data" :props="defaultProps" show-checkbox check-on-click-node node-key="fileName"
                :check-strictly="true" ref="tree" @check-change="" @node-click="handleNodeClick"
                @check="handleCheckboxClick" :default-expand-all="true">
              </el-tree>
            </div>
          </el-col>
          <el-col :span="19">
            <iframe :src="imgfs" frameborder="0" style="height: 90vh; width: 100%;margin-top: 50px;"></iframe>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="返回" name="return"></el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import SLForm from "../components/BasicForm.vue";
import GCForm from "../components/GCForm.vue";
import BJForm from "../components/BJForm.vue";
import LQForm from "../components/LQForm.vue";
import GDForm from "@/components/GDForm.vue";
import { getAllFilePaths, buildExcelDocument2, uploadFile, removeFile ,getUploadFileInfo} from "../api/first";
export default {
  components: {
    SLForm,
    GCForm,
    BJForm,
    LQForm,
    GDForm,
  },
  data() {
    return {
      activeName: "fourth",
      fileData: "",
      
      data: [],
      imgfs: "",
      defaultProps: {
        children: "listFiles",
        label: "fileName",
      },
      parameterMajor_id: [],
      fileName: "",
      fileList: [],
      setDeleteids: [],
      files: {},
      tree: [],
      input2: '',
      isFinite2:false,
      isFinite1:false
    };
  },
  methods: {
   
    handleClick(tab, event) {
      switch (tab.name) {
        case "second":
          this.$router.push({
            path: "./second",
            query: { sblsh: localStorage.getItem("sblsh") },
          });
          break;
        case "third":
          this.$router.push({
            path: "./third",
            query: { sblsh: localStorage.getItem("sblsh") },
          });
          break;
        case "fourth":
          this.$router.push({
            path: "./fourth",
            query: { sblsh: localStorage.getItem("sblsh") },
          });
          break;
        case "first":
          this.$router.push({
            path: "./first",
            query: { sblsh: localStorage.getItem("sblsh") },
          });
          break;
          case "return":
          this.$router.push({
            path: "./metadataManagement",
            // query: { sblsh: localStorage.getItem("sblsh") },
          });
          break;
      }
    },
    tui() {
      this.$router.push({ path: "./metadataManagement" });
      // this.$router.back()
    },
    action() {
      return process.env.VUE_APP_FILE_BASE_API + "/uploadApi/uploadFile"
    },
    getTree() {
      getAllFilePaths({ sblsh: localStorage.getItem("sblsh") }).then((res) => {
        if (res.code == 200) {
          this.data = res.data;
        }
      });
    },
    handleNodeClick(data) {
      this.$refs.tree.setCheckedNodes([data]);
      let id = this.$refs.tree.getCheckedKeys();
      console.log(data)
      if(data.fileName.indexOf('pdf') !== -1){
        this.isFinite1 =true 
        this.isFinite2 = false
      } else {
        this.isFinite2 =true
        this.isFinite1 =false
      }
      this.tree = id;
      this.files = data;
      if (id instanceof Array && id[0] === data.fileName) {
        this.handleCheckChange(data, false);
      } else {
        this.handleCheckChange(data, true);
      }
      if (data.fileName.indexOf(".") !== -1) {
        this.imgfs = process.env.VUE_APP_FILE_API + "/" + data.path;
      }
    },
    //点击复选框时
    handleCheckboxClick(data, obj) {
      this.handleNodeClick(data)
      this.$refs.tree.setCheckedKeys(data);
    },
    handleCheckChange(data, checked) {
      const _this = this;
      if (checked) {
        this.fileName = data.fileName;
        this.$refs.tree.setCheckedKeys([data.fileName]);
      } else {
        if (this.fileName === data.fileName) {
          this.parameterMajor_id = [];
          this.fileName = "";
          this.$refs.tree.setCheckedKeys([]);
        }
      }
    },
   
    handleRemove() {

      var arry = this.$refs.tree.getCheckedNodes(true);
      let filePath = [];
      arry.forEach(v => {
        filePath.push(v.path);
      })
      var formData = new FormData();
      formData.set("path", filePath);
      formData.set("sblsh", localStorage.getItem("sblsh"))
      this.$confirm("此操作为材料删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          removeFile(formData).then(res => {
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: res.msg
              })
              this.getTree();
            } else {
              this.$message({
                type: 'warning',
                message: res.msg
              })
            }
          })
        }).catch()
    },
    beforeRemove(file, fileList) {
      console.log(11)
      let index = this.fileList.findIndex(
        (fileItem) => fileItem.uid === file.uid
      );
      this.fileList.splice(index, 1);
    },
    fileChange(file, fileList) {
      console.log(22)
      this.fileList = fileList;
      let fileName = file.name;
      let uid = file.uid
      let pos = fileName.lastIndexOf(".");
      let lastName = fileName.substring(pos, fileName.length);
      if (
        lastName.toLowerCase() !== ".doc" &&
        lastName.toLowerCase() !== ".docx" &&
        lastName.toLowerCase() !== ".xls" &&
        lastName.toLowerCase() !== ".xlsx" &&
        lastName.toLowerCase() !== ".pdf" &&
        lastName.toLowerCase() !== ".png" &&
        lastName.toLowerCase() !== ".jpg"
      ) {
        this.$message.error("文件必须为.doc .docx .xls .xlsx .pdf .png jpg 类型");
        // this.resetCompressData()
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].uid == uid) {
            fileList.splice(i, 1)
          }
        }
        this.fileList = [];
        return;
      }
      // 限制上传文件的大小
      const isLt =
        file.size / 1024 / 0 >= 1 && file.size / 1024 / 1024 / 5 <= 1;
      if (!isLt) {
        this.$message.error("上传文件大小不得小于0KB,不得大于5MB!");
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].uid == uid) {
            fileList.splice(i, 1)
          }
        }
        this.fileList = [];
      }
      let existFile = fileList.slice(0, fileList.length - 1).find(f => f.name === file.name)//如果文件名重复
      if (existFile) {
        this.$message.error('当前文件已经存在!');
        fileList.pop()
      }
      console.log(fileList)
      this.fileList = fileList
    },
    beforeUpload(file, fileList) {
      return isLt;
    },
    submitUpload() {
      if (this.tree.length > 0 && this.files.fileName.indexOf(".") == -1) {
        let formData = new FormData();
        formData.append("path", this.files.path);
        formData.append("file", this.fileList);
        formData.append("sblsh", localStorage.getItem("sblsh"));
        this.fileList.forEach((items) => {
          formData.append("file", items.raw);
        });
        this.getUploadFile(formData);
        console.log(formData.get("path"), formData.get("file"), this.fileList);
      } else {
        this.$message({
          message: "请选择目录",
          type: "warning",
        });
      }
    },
    getUploadFile(data) {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector(".el-tabs__content"), //loadin覆盖的dom元素节点
      });
      uploadFile(data).then((res) => {
        loading.close()
        if (res.code !== 200) {
          this.$message({
            message: res.msg,
            type: "error",
          });
        } else {
          this.$message({
            message: res.msg,
            type: "success",
          });
        }
        this.fileList = [];
        this.getTree();
      });
    },
    save() { },
    cancel() { }
  },
  created() {
    this.getUploadFileInfo()
    this.getTree();
  },

};
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}

.el-tree {
  display: inline-block;
  min-width: 100%;
  /* width: 200px;
  height: 100px; */
  
}


.dic {
  overflow-y: scroll;
  height: 94vh;
  /* overflow: auto; */
}

/deep/.el-tree-node__label {
  font-size: 18px;
  padding: 3px 0;
}
.el-tree-node /deep/{
  padding: 3px 0;
  display: inline !important;
}
/* :deep(.el-tree-node is-expanded is-focusable){
  display: inline !important;
} */

/* /deep/.el-tree-node__label :hover{
  z-index: 9999;
} */

.el-upload__tip {
  font-size: 16px;
}


::v-deep #tab-return {
  margin-left: 1200px;
  /* border: #3a8ee6 solid 1px; */
  padding: 5px 15px;
  align-content: center;
  margin-top: 2px;
  border-radius: 5px;
  text-align: center;
  line-height: 20px;
  font-size: medium;
}

/*/deep/.el-upload-list--text{
  margin-bottom: 7px;
}
*/
/* /deep/.el-tree-node__expand-icon.is-leaf {
  color: #666;
}
/deep/.el-tree .el-tree-node .is-leaf + .el-checkbox .el-checkbox__inner{display: none;}
/deep/.el-tree .el-tree-node .el-checkbox .el-checkbox__inner{display: inline-block;}
/deep/.el-tree-node__expand-icon.is-leaf {
   color: transparent;
} */
</style>
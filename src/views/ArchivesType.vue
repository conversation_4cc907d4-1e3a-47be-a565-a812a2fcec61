<template>
  <!-- 档案类型 -->
  <div class="divBox">
    <div class="searchDiv1">
      <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
        <el-form-item label="代码">
          <el-input v-model="query.code" placeholder="代码"></el-input>
        </el-form-item>
        <el-form-item label="档案类型">
          <el-input v-model="query.name" placeholder="档案类型"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" class="tableClass" :height="tableHeight" :header-cell-style="{ background: '#F6F7FA' }">
      <el-table-column prop="code" label="代码" align="center"></el-table-column>
      <el-table-column prop="name" label="档案类型" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="sequence" label="排序" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleRemove(scope.$index, scope.row)">删除</el-button>
          <el-button type="text" icon="el-icon-folder" @click="handleViewItemCode(scope.$index, scope.row)">zip目录
          </el-button>
          <el-button type="text" icon="el-icon-document" @click="handleEditReceiptCatalog(scope.$index, scope.row)">
            回执收据
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:page-size="query.pageSize" :page-sizes="[10, 20, 50, 100]"
     layout="total, sizes, prev, pager, next, jumper"
      :total="query.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" style="float: right;margin-top: 10px;"/>
    <!-- 档案类型编辑 -->
    <el-dialog :title="dialog.title" :visible.sync="dialog.dialogVisible" width="30%" :before-close="handleClose"
      >
      <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" size="small" label-width="100px"
        class="demo-ruleForm">
        <el-form-item label="档案类型" prop="name">
          <el-input v-model="ruleForm.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="档案代码" prop="code">
          <el-input v-model="ruleForm.code" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sequence">
          <el-input v-model="ruleForm.sequence" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </span>
    </el-dialog>
    <!-- 事项列表 -->
    <el-dialog :title="itemCodeDialog.title" :visible.sync="itemCodeDialog.dialogVisible" fullscreen
               :before-close="handleClose" :append-to-body="true">
      <zipPackage :archivesType="ruleForm.id" v-if="itemCodeDialog.dialogVisible"></zipPackage>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </span>
    </el-dialog>
    <!-- 回执配置 -->
    <el-dialog :title="receiptCatalogDialog.title" :visible.sync="receiptCatalogDialog.dialogVisible" fullscreen
               :before-close="handleClose" :append-to-body="true">
      <receiptCatalogConfig :taskType="ruleForm.code" v-if="receiptCatalogDialog.dialogVisible"></receiptCatalogConfig>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import zipPackage from "../components/ZipPackage.vue";
import receiptCatalogConfig from "../components/ReceiptCatalogConfig.vue";
import {archivesTypeApi} from "../api/archivesType";
export default {
  components: {
    zipPackage,
    receiptCatalogConfig,
  },
  data() {
    return {
      tableHeight: 0,
      query: {
        pageNo: 0,
        pageSize: 10,
        total: 0,
      },
      itemCodeDialog: {
        title: "目录配置",
        dialogVisible: false,
      },
      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      receiptCatalogDialog: {
        title: "回执收据配置",
        dialogVisible: false,
      },
      rules: {
        name: [{required: true, message: "请输入档案类型", trigger: "blur"}],
        code: [{required: true, message: "请输入档案代码", trigger: "blur"}],
        sequence: [{required: true, message: "请输入排序号", trigger: "blur"}, {
          validator: this.checkPrice,
          trigger: 'change'
        }],
      },
      ruleForm: {},
      tableData: [],
    };
  },
  mounted() {
    this.initTableHeight();
  },
  created() {
    this.getData();
  },
  methods: {
    //验证数字
    checkPrice(rule, value, callback) {
      if (value) {
        let rgx = /^-?[1-9]\d*$/;
        if (value.toString().match(rgx) == null) {
          return callback(new Error('请检查输入格式，必须为整数'))
        } else {
          callback();
        }
      }
    },
    //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      this.$nextTick(() => {
        let tempHeight = height - 45 - 50 - 51;
        this.tableHeight = tempHeight;
      });
    },
    handleAdd() {
      this.dialog.title = "添加";
      this.ruleForm = {};
      this.ruleForm.qxId = "07dfbb40-b179-4f1d-98b3-80891a8204d4";
      this.$nextTick(()=>{
        this.$refs.ruleForm.clearValidate();
      })
      this.dialog.dialogVisible = true;
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    handleEdit(index, row) {
      this.dialog.title = "编辑";
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.dialog.dialogVisible = true;
    },
    handleClose(done) {
      this.form = {};
      done();
    },
    handleViewItemCode(index, row) {
      this.ruleForm = row;
      this.itemCodeDialog.dialogVisible = true;
    },
    handleEditReceiptCatalog(index, row) {
      this.ruleForm.code = row.code
      this.receiptCatalogDialog.dialogVisible = true
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            archivesTypeApi.remove({id: row.id}).then((a) => {
              if (a.code == 200) {
                this.$message.success("删除成功");
              this.query.pageNo = 0;
              this.getData();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => { });
    },
    cancel() {
      this.form = {};
      this.itemCodeDialog.dialogVisible = false;
      this.dialog.dialogVisible = false;
      this.receiptCatalogDialog.dialogVisible = false;
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          archivesTypeApi.save(this.ruleForm).then((a) => {
            if (a.code == 200) {
              this.$message.success("保存成功");
              this.query.pageNo = 0;
              this.cancel();
              this.ruleForm = {};
              this.getData();
            } else {
              this.$message.error("保存失败");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getData() {
      archivesTypeApi.page(this.query).then((a) => {
        if (a.code === 200) {
          this.tableData = a.data;
          this.query.total = a.totalCount;
        }
      });
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1 ;
      this.query.pageNoTemp = val;
      this.getData();
    },
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}

/* .searchDiv {
  margin-top: 10px;
} */
.divBox{
  padding: 15px;
  box-sizing: border-box;
}
.tableClass {
  width: 100%;
  /* padding: 0 20px; */
}
</style>
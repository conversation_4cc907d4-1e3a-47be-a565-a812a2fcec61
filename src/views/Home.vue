<template>
  <div>
<el-container :style="{height:viewHeight+'px'}">
  <el-aside width="260px" style="background-color: #1a6bac" >
    <Menu></Menu>
  </el-aside>
  
  <el-container>
    <el-header style="background-color:#1a6bac; text-align: right; font-size: 12px">
      <el-dropdown @command="handleCommand">
        <i class="el-icon-setting" style="margin-right: 15px;color:white"><span>{{name}}</span></i>
        <el-dropdown-menu slot="dropdown"> 
          <el-dropdown-item command="out">退出</el-dropdown-item> 
        </el-dropdown-menu>
      </el-dropdown>
      
    </el-header>
    
    <el-main> 
      <transition name="move" mode="out-in">
        <!-- <InFeedTreeCommon></InFeedTreeCommon> -->
                    <keep-alive > 
                        <router-view></router-view>
                    </keep-alive>
      </transition>
    </el-main>
  </el-container>
</el-container>

  </div>
</template>

<style>
  .el-header {
    background-color: #B3C0D1;
    color: #333;
    line-height: 60px;
  }
  
  .el-aside {
    color: #333;
  }
</style>

<script>  
import Menu from '../components/Menu.vue'
  export default {
    mounted(){
      this.viewHeight=document.documentElement.clientHeight;
    }, 
    components:{
      Menu, 
    },
    data() { 
      return {
        //屏幕高度
        viewHeight:0, 
        name:sessionStorage.getItem("username")
      }
    },
    methods:{
      handleCommand(com){
        sessionStorage.removeItem("username");
        this.$router.push('/login');
      }
    }
  };
</script>
<template>
  <!-- 数据同步 -->
  <div class="about">
    <div class="searchDiv">
      <el-form
        :inline="true"
        :model="query"
        class="demo-form-inline"
        size="small"
      >
        <el-form-item label="流水号">
          <el-input v-model="query.keyWord" placeholder="部门编码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">同步</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%" height="700" border>
      <el-table-column
        prop="sblsh"
        label="流水号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="itemCode"
        label="事项编码"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="stage"
        label="所属过程"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="result"
        label="是否成功"
        align="center"
      ></el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleView(scope.$index, scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      fullscreen
      :title="dialog.title"
      :visible.sync="dialog.dialogVisible"
      center
    >
      <el-form
        :model="form"
        status-icon
        ref="ruleForm"
        size="small"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="流水号" prop="bmbm">
          <el-input
            v-model="form.sblsh"
            autocomplete="off"
            :disabled="true"
          ></el-input>
        </el-form-item>
        <el-form-item label="事项编码" prop="name">
          <el-input
            v-model="form.itemCode"
            autocomplete="off"
            :disabled="true"
          ></el-input>
        </el-form-item>
        <el-form-item label="同步时间" prop="qzh">
          <el-input v-model="form.createTime" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="所属过程" prop="qzh">
          <el-input v-model="form.stage" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="qzh">
          <el-input
            type="textarea"
            autosize
            v-model="form.content"
            :disabled="true"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="form.stage == '生成zip' && form.result == '成功'">
          <el-button type="primary" @click="downZip(form.content)"
            >下载zip包</el-button
          >
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  created() {},
  data() {
    return {
      tableData: [],
      websock: null,
      query: {
        userId: "1",
        keyWord: null,
        type: 0,
      },
      form: {},
      dialog: {
        dialogVisible: false,
        title: "查看 ",
      },
    };
  },
  methods: {
    cancel() {
      this.form = {};
      this.dialog.dialogVisible = false;
    },
    search() {
      let obj = this.query;
      let array = new Array();
      Object.keys(obj).forEach(function (key) {
        array.push(obj[key]);
      });
      let str = array.join("/");
      this.initSocket("/" + str);
    },
    //获取数据
    initSocket(val) {
      // console.log( process.env.VUE_APP_SOCKET)
      // debugger
      this.tableData = [];
      let wsuri = "";
      if (process.env.NODE_ENV == "development") {
        wsuri = "ws://localhost:8087/sync/1/1/0";
      } else {
        wsuri = "ws://*************:8087/sync" + val;
      }
      this.websock = new WebSocket(wsuri);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      //连接建立之后执行send方法发送数据
      //   this.websocketsend(JSON.stringify(actions));
    },
    websocketonerror() {
      //连接建立失败重连
      this.initWebSocket();
    },
    websocketonmessage(e) {
      //数据接收
      if (e.data != "连接成功") {
        let data = JSON.parse(e.data);
        console.log(e);
        this.tableData.push({
          content: data.content,
          createTime: data.creatTime,
          sblsh: data.sblsh,
          itemCode: data.itemCode,
          stage: data.stage,
          result: data.result == 0 ? "成功" : "失败",
        });
        if (data.stage == "生成zip") {
          this.websock.close();
        }
      }
    },

    websocketsend(Data) {
      //数据发送
      this.websock.send(Data);
    },
    websocketclose(e) {
      //关闭
      console.log("断开连接", e);
    },
    //获取同步数据
    getSycnData() {},
    handleView(index, row) {
      this.form = row;
      this.dialog.dialogVisible = true;
    },
    downZip(url) {
      let urlRequst = "";
      if (process.env.NODE_ENV == "development") {
        urlRequst =
          "http://localhost:8088/api/downloadApi/downFile?address=" + url;
      } else {
        urlRequst =
          "http://*************:8088/api/downloadApi/downFile?address=" + url;
      }
      window.location.href = urlRequst;
    },
  },
};
</script>

<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="归档配置信息" name="pz">
        <XMLTree type="pz" :tableDatas="tableDatas" />
      </el-tab-pane>
      <el-tab-pane label="基本信息" name="basic"
        ><XMLTree type="basic"
      /></el-tab-pane>
      <el-tab-pane label="办理流程信息" name="gc"
        ><XMLTree type="gc"
      /></el-tab-pane>
    </el-tabs>
    <div style="" class="btn">
      <el-button type="primary" @click="saveIds">新增</el-button>
      <el-button @click="dialog.dialogVisible = false">取 消</el-button>
    </div>
    <el-dialog
      width="30%"
      center
      :close-on-press-escape="false"
      title="新增"
      :visible.sync="addDialogVisible"
      :before-close="handleClose"
      :append-to-body="true"
      :close-on-click-modal="true"
    >
      <el-form
        :model="addForm"
        :rules="rules2"
        ref="ruleForm"
        label-width="110px"
        class="demo-ruleForm"
      >
        <el-form-item label="元数据标签名" prop="setting_name">
          <el-input v-model="addForm.setting_name"></el-input>
        </el-form-item>
        <el-form-item label="元数据标题" prop="note">
          <el-input v-model="addForm.note"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSaveIds('ruleForm')"
          >确定</el-button
        >
        <el-button @click="removeForm('ruleForm')">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import XMLTree from "../components/XMLTree.vue";
import { getMatedateAll, addMatedate } from "../api/first";
export default {
  components: {
    XMLTree,
  },
  data() {
    return {
      activeName: "pz",
      tableDatas: [],
      addDialogVisible: false,
      rules2: {
        setting_name: [
          { required: true, message: "请输入元数据标签名", trigger: "blur" },
        ],
        note: [
          { required: true, message: "请输入元数据标题", trigger: "blur" },
        ],
      },
      addForm: {},
    };
  },
  created() {
    this.getMatedateA();
  },
  methods: {
    handleClick(tab, event) {},
    //获取元数据
    getMatedateA() {
      getMatedateAll({itemsCode:localStorage.getItem('taskCode')}).then((res) => {
        console.log(res);
        this.tableDatas = res.data;
      });
    },
    handleClose(ruleForm) {
      this.form = {};
      this.addDialogVisible = false;
      this.$refs["ruleForm"].resetFields();
    },
    removeForm(ruleForm) {
      this.addDialogVisible = false;
      this.getMatedateA()
      this.$refs["ruleForm"].resetFields();
    },
    addSaveIds() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.addForm.items_code = localStorage.getItem("taskCode");
          addMatedate(this.addForm).then((res) => {
            if (res.code == 200) {
              this.$message({
                message: "添加成功",
                type: "success",
              });
              this.addForm = {};
              this.addDialogVisible = false;
              this.$refs["ruleForm"].resetFields();
              this.getMatedateA()
            } else {
              this.$message({
                message: "添加失败",
                type: "warning",
              });
              this.addForm = {};
              this.addDialogVisible = false;
              this.$refs["ruleForm"].resetFields();
            }
          });
        }
      });
    },
    saveIds(ruleForm) {
      // this.selectMetadata.dialogVisible = false;
      this.addDialogVisible = true;
    },
  },
};
</script>
<style scoped>
.btn{
    margin-top: 35px;
    width: 100%;
    display: flex;
    justify-content: center;
}
</style>
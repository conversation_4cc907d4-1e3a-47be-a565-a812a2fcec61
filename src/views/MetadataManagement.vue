<template>
  <div class="mote box">
    <el-card
      class="readyElCard"
      style="margin-top: 10px;
      padding: 0px 0px;
      width: 20%;
      height: 97%;
      overflow-y: scroll;
      overflow-x: scroll;"
      >
      <div class="down-tree" ref="downTree">
          <TreeCommon @nodeClick="nodeClick" v-model="treeD"></TreeCommon>
        </div>
    </el-card>
    <el-card class="readyFileCard">
      <div class="tableDiv">
      <el-row :gutter="10">

        <el-col :xs="{span:24,offset:0}" :sm="{span:8,offset:0}" :md="{span:24,offset:0}">
          <div class="searchDiv1">
          <!--统计数据-->
          <div style="margin-bottom: 10px">
            <el-tooltip placement="right">
              <div slot="content">采集总数=(采集中 + 已清点 + 已驳回)</div>
              <i class="el-icon-question icon-color"></i>
            </el-tooltip>
            <b style="font-size:15px;margin-left: 0px;">采集总数: {{ dataStatistics.totalCount }}</b>

            <b style="font-size:15px;margin-left: 50px;">采集中: {{ dataStatistics.collectingCount }}</b>

            <el-tooltip placement="left" style="font-size:15px;margin-left: 50px;">
              <span slot="content">已清点=(缺失材料 + 已移交 + 移交失败)</span>
              <i class="el-icon-question icon-color"></i>
            </el-tooltip>

            <b style="font-size:15px;margin-left: 0px;">已清点: {{ dataStatistics.archivesCount }}</b>


            <b style="font-size:15px;margin-left: 50px;">缺失材料: {{ dataStatistics.qsclCount }}</b>
            <b style="font-size:15px;margin-left: 50px;">待移交: {{ dataStatistics.noHandoverCount }}</b>
            <b style="font-size:15px;margin-left: 50px;">已移交: {{ dataStatistics.handoverCount }}</b>
            <b style="font-size:15px;margin-left: 50px;">移交失败: {{ dataStatistics.handoverFailCount }}</b>
            <b style="font-size:15px;margin-left: 50px;">已驳回: {{ dataStatistics.disapproveCount }}</b>
          </div>
          <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
            <el-form-item :label="query.labelOne" v-if="query.type == 0">
              <el-input v-model="query.bmbm" placeholder="部门编码" disabled style="width: 180px;"></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 0">
              <el-input v-model="query.name" placeholder="部门编码" disabled></el-input>
            </el-form-item>
            <el-form-item :label="query.labelOne" v-if="query.type == 1">
              <el-input v-model="query.itemCode" placeholder="部门编码" disabled style="width: 180px;"></el-input>
            </el-form-item>
            <el-form-item :label="query.labelTwo" v-if="query.type == 1">
              <el-input v-model="query.itemName" placeholder="部门编码" disabled style="width: 180px;"></el-input>
            </el-form-item>
            <el-form-item label="流水号">
              <el-input v-model="query.sblsh" placeholder="流水号" style="width: 180px;"></el-input>
            </el-form-item>
            <el-form-item label="档案号">
              <el-input v-model="query.archivesNo" placeholder="档案号" style="width: 180px;"></el-input>
            </el-form-item>
            <el-form-item label="清点状态">
              <el-select v-model="query.archivesCount" clearable  class="m-2" placeholder="清点状态" style="width: 180px;">
                <el-option label="已清点" :value="1"></el-option>
                <el-option label="未清点" :value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="清点结果">
              <el-select v-model="query.archivesCountRes" clearable  class="m-2" placeholder="清点结果" style="width: 180px;">
                <el-option label="缺失材料" :value="1"></el-option>
                <el-option label="清点通过" :value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="移交状态">
              <el-select v-model="query.handOver" clearable placeholder="移交状态" style="width: 180px;">
                <el-option label="未移交" :value="0"></el-option>
                <el-option label="已移交" :value="1"></el-option>
                <el-option label="移交失败" :value="2"></el-option>
                <el-option label="正在移交" :value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="收件时间">
                <el-date-picker
                  v-model="timeRangeValue"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 350px;"
                  @change="handleTimeChange"
                  >
                </el-date-picker>
            </el-form-item>
            <el-form-item label="办结状态">
              <el-select v-model="query.resultType" clearable  class="m-2" placeholder="办结状态" style="width: 180px;">
                <el-option label="已办结" :value="1"></el-option>
                <el-option label="已驳回" :value="0"></el-option>
                <el-option label="办理中" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        </el-col>
        <el-col :xs="{span:24,offset:0}" :sm="{span:8,offset:0}" :md="{span:24,offset:0}">
        <el-table
            :data="tableData"
            style="width: 100%"
            :row-style="{height:0+'px'}"
            :cell-style="{padding:0+'px'}"
            :height="tableHeight +'px'"
            :header-cell-style="{ background: '#F6F7FA' ,color:'#606266'}"
            id="tableData"
            ref="tables">
          <el-table-column  width="80px" label="序号" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column width="180px" prop="sblsh" label="申办流水号" align="center"></el-table-column>
          <el-table-column width="200px" prop="applyDate" label="收件时间" align="center">
            <template slot-scope="scope">
              {{ scope.row.applyDate === null ? scope.row.sbTime : scope.row.applyDate }}
            </template>
          </el-table-column>
          <el-table-column width="200px" prop="taskName" label="事项名称" align="center"></el-table-column>
          <!--          <el-table-column width="200px" prop="contactcode" label="证件号码" align="center"></el-table-column>-->
          <el-table-column width="200px" prop="year" label="年度" align="center"></el-table-column>
          <el-table-column width="450px" prop="documentNumber" label="电子文件号" align="center"></el-table-column>
          <el-table-column prop="name" label="保管期限" align="center"></el-table-column>
          <el-table-column prop="archivesNo" label="档案号" align="center"></el-table-column>
          <el-table-column width="200px" prop="handOverTime" label="移交时间" align="center">
            <template slot-scope="scope">
              {{ scope.row.handOverTime === null ? '无' : scope.row.handOverTime }}
            </template>
          </el-table-column>
          <el-table-column width="200px" prop="createTime" label="采集时间" align="center">
            <template slot-scope="scope">
              {{scope.row.createTime === null ? '无' : scope.row.createTime}}
            </template>
          </el-table-column>


          <el-table-column  prop="archivesCount" label="清点状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.archivesCount == 1">已清点</span>
              <span v-else>未清点</span>
            </template>
          </el-table-column>
          <el-table-column prop="archivesCount" label="移交状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.handOver == `0`">未移交</span>
              <span v-if="scope.row.handOver == `1`">已移交</span>
              <span v-if="scope.row.handOver == `2`">移交失败</span>
              <span v-if="scope.row.handOver == `3`">正在移交</span>
            </template>
          </el-table-column>

          <el-table-column prop="handOverMsg" label="移交消息" align="center">
            <template>
              <template slot-scope="scope">
                {{ scope.row.handOverMsg == null ? '无' : scope.row.handOverMsg }}
              </template>
            </template>
          </el-table-column>
          <el-table-column fixed="right" prop="archivesCountRes" label="清点结果" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.archivesCountRes == 1" style="font-weight: bolder;color: red">缺失材料</span>
              <span v-else-if="scope.row.archivesCountRes == 0"
                    style="font-weight: bolder;color: green;">清点通过</span>
              <span v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" prop="archivesCount" label="办结状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.resultType == `4`" style="font-weight: bolder;color: gray">办理中</span>
              <span v-if="scope.row.resultType == `1`" style="font-weight: bolder;color: green">已办结</span>
              <span v-if="scope.row.resultType == `0`" style="font-weight: bolder;color: red">已驳回</span>
              <span v-if="scope.row.resultType == `2`" style="font-weight: bolder;color: red">转报</span>
              <span v-if="scope.row.resultType == `3`" style="font-weight: bolder;color: red">终止办件</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" prop="archivesCount" label="同步状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isFile == 0" style="font-weight: bolder;color: gray">正在重新同步中</span>
              <span v-if="scope.row.isFile == 1 && scope.row.resultType != `0` && scope.row.resultType != `3`"
                    style="font-weight: bolder;color: gray">正在同步中</span>
              <span v-if="scope.row.isFile == 2 ">已同步</span>
              <span v-if="scope.row.isFile == 3 ">已同步</span>
            </template>
          </el-table-column>
          <el-table-column  label="操作" fixed="right" align="center" width="150">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-view" @click="handleManagement(scope.$index, scope.row)">查看</el-button>
              <el-button type="text" icon="el-icon-thumb" :disabled="scope.row.isFile == 1  || scope.row.archivesCount !=1"
                         @click="handleArchivesCountResult(scope.$index, scope.row)">清点
              </el-button>

              <el-popover placement="top" width="170" trigger="click">
                <el-button type="text" icon="el-icon-refresh" :disabled="scope.row.isFile == 1  || scope.row.archivesCount !=1"
                           @click="reSync(scope.$index, scope.row)">重新同步
                </el-button>

                <el-button type="text" icon="el-icon-reading" :disabled="scope.row.isFile == 1  || scope.row.archivesCount !=1"
                           @click="handleTest(scope.$index, scope.row)">检测结果
                </el-button>

                <el-button type="text" icon="el-icon-document-checked" :disabled="scope.row.isFile == 1  || scope.row.archivesCount !=1"
                  @click="handleCreateZip(scope.$index, scope.row)">生成zip
                </el-button>

                <el-button type="text" icon="el-icon-s-promotion" :disabled="
                  scope.row.isFile ==1 || scope.row.archivesCountRes == 1
                " @click="handleTransferZip(scope.$index, scope.row.sblsh)">移交
                </el-button>

                <el-button type="text" icon="el-icon-more" slot="reference">更多</el-button>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
          <el-pagination v-model:page-size="query.pageSize" :page-sizes="[10, 20, 50, 100]"
                         layout="total, sizes, prev, pager, next, jumper" :total="query.total" @size-change="handleSizeChange"
                         @current-change="handleCurrentChange" style="float: right;margin-top: 10px;"/>
        </el-col>
      </el-row>
      </div>
    </el-card>
<!--    四性检测-->
    <el-dialog
        :close-on-click-modal="false"
        width="1100px"
      custom-class="resultDialog"
      :title="fourTestDialog.title" 
      :visible.sync="fourTestDialog.dialogVisible" 
      :append-to-body="true"
      center
      >
      <el-table 
        class="resultTable"
        :data="fourTestTableData" 
        :row-style="{ height: '0' }" 
        :cell-style="{ padding: '0' }" 
        width="100%"
        height="450"
      >
        <el-table-column label="检测ID" align="center" prop="checkId">
        </el-table-column>
        <el-table-column label="检测类型" align="center" prop="four_check">
        </el-table-column>
        <el-table-column label="检测项目" align="center" prop="check_options">
        </el-table-column>
        <el-table-column label="检测内容" align="center" prop="content" show-overflow-tooltip>
        </el-table-column>
        <el-table-column width="400px" label="检测结果" align="center" prop="isPassed">
           <template slot-scope="scope">
            <span v-if="scope.row.isPassed == true" style="color: green;">通过</span>
            <span v-if="scope.row.isPassed == false" style="color: red;">
              <ul>
                <li v-for="item in scope.row.checkResults">{{item}}</li>
              </ul>
            </span>
          </template>
<!--          <span style="color: green;">通过</span>-->
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelFourTest">取 消</el-button>
      </span>
    </el-dialog>
    <!--   清点   -->
    <el-dialog :title="archivesCountDialog.title"
               v-dialogDrag
               :close-on-click-modal="false"
               :visible.sync="archivesCountDialog.dialogVisible"
               :append-to-body="true" center>
      <el-upload ref="upload" style="width: 50%;color: #409EFF;" class="upload-demo" action="#"
                 accept=".doc,.docx,.xls,.xlsx,.pdf,.png,.jpg" :limit="1" :show-file-list="false"
                 :http-request="submitUpload" :on-change="fileChange">
      </el-upload>
      <div><span
          style="{font-weight:500;color: red;}">上传文件支持格式：.doc .docx .xls .xlsx .pdf .png jpg 单个文件不能超过5MB</span>
      </div>
      <div><span style="{font-weight:500;color: red;}">提示：上传文件后会自动处理，如需查看请稍后操作</span></div>
      <el-table ref="tables" :row-style="{ height: '0' }" :cell-style="{ padding: '0' }" width="100%"
                :data="archivesCountData" class="tableClass" height="300" @row-click="tableClick"
                :expand-row-keys="expends"
                @expand-change='getexpand'>
        <el-table-column type="expand" :filter-method="filterColumn">
          <template v-if="scope.row.fileName.length != 0" slot-scope="scope">
            <div class="countDiv" v-for="(item, index) in scope.row.fileName" :key="index">
              <p style="width: 50%;margin: 0;margin-left: 25px;" :class="item.url != undefined ? 'tiet' : 'tiet2'">{{
                  index + 1
                }}、{{ item.name }}</p>
              <span v-if="!item.url" style="color: #409EFF;" id="checkFile"
                    @click="checkFile(scope.row, item)">上传文件</span>
              <p v-if="item.url" style="width: 50%;color: #409EFF;margin: 0;text-align: center;"
              ><span style="cursor: pointer;">√</span></p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="目录" align="center" prop="dir">
        </el-table-column>
        <el-table-column label="状态" align="center" prop="fileName">
          <template slot-scope="scope">
            <span style="color: green;font-weight: 1000" v-if="scope.row.fileName.length == 0">√</span>
            <span style="color: red;font-weight: 1000" v-if="scope.row.fileName.length != 0">缺少材料</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="archivesCountDialog.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
import SLForm from "../components/BasicForm.vue";
import GCForm from "../components/GCForm.vue";
import BJForm from "../components/BJForm.vue";
import LQForm from "../components/LQForm.vue";
import ItemConfiguration from "../components/ItemConfiguration.vue";
import TreeCommon from "../components/TreeCommon.vue";
import Business from "../components/Business.vue";
import { itemBusinessApi } from "../api/itemBusiness";
import { tool } from "../utils/tool";
import { materialsBillApi } from "../api/materialsBill";
import { logApi } from "../api/log";
import GDForm from "../components/GDForm";
import { getResults, uploadFile, removeFile } from "../api/first";
// import { uploadFile} from "../api/first";
import "../utils/dialogdrag"
export default {
  components: {
    TreeCommon,
    Business,
    SLForm,
    GCForm,
    BJForm,
    LQForm,
    ItemConfiguration,
    GDForm,
    isStatus: false
  },
  data() {
    return {
      dataStatistics:{},
      timeRangeValue:[],
      expends: [],
      tableHeight: 700,
      activeName: "first",
      baseForm: {},
      isCreateZip: false,
      baseTableData: [],
      matterialTableData: [],
      treeData: [],
      treeD: '',
      archivesCountData: [],
      logQuery: {
        pageNo: 0,
        pageSize: 10,
      },
      query: {
        pageNo: 0,
        pageSize: 10,
        total: 0,
        labelOne: "",
        labelTwo: "",
        type: null,
        sblsh: "",
      },
      showColumn: false,
      dialog: {
        title: "数据管理",
        dialogVisible: false,
      },
      fourTestDialog: {
        title: "检测结果",
        dialogVisible: false,
      },
      archivesCountDialog: {
        title: "清点结果",
        dialogVisible: false,
      },
      fourTestTableData: [],
      rules: {
        bmbm: [{ required: true, message: "请输入部门编码", trigger: "blur" }],
        name: [{ required: true, message: "请输入部门名称", trigger: "blur" }],
        qzh: [{ required: true, message: "请输入全宗号", trigger: "blur" }],
      },
      form: {},
      SLForm: {},
      tableData: [],
      GCForm: {},
      BJForm: {},
      LQForm: {},
      GDForm: {},
      liu: {},
      fileList: [],
      obj: {},
      dz: [],
      scrollTop: ''
      // isBtn:false
    };
  },
  created() {

  },
  watch: {
  },
  mounted() {
    this.getAutoHeight();
  },
  activated () {
    this.$nextTick(() => {
      setTimeout(() => {
        var scrollTop = this.$refs.tables
        scrollTop.bodyWrapper.scrollTop = this.scrollTop
      }, 13)
    })
  },
  methods: {
    //重新同步业务
    reSync(index, row) {
      this.$confirm("此操作将重新同步电子文件信息, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        itemBusinessApi.reSync({sblsh: row.sblsh}).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
          this.getData()
        })
      })

    },
    findDataStatistics() {
      itemBusinessApi.findDataStatistics(this.query).then(res => {
        this.dataStatistics = res.data;
      })
    },
    handleTimeChange(val) {
      if (val != null) {
        this.query.applyStartTime = val[0]
        this.query.applyEndTime = val[1]
      } else {
        this.query.applyStartTime = ""
        this.query.applyEndTime = ""
      }
    },
    handleScroll() {
      //获取元素高度  this.$refs.init.offsetHeight
      this.scrollTop = this.$refs.tables.bodyWrapper.scrollTop
    },
    getUploadFile(data) {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "正在加载数据中...",
        target: document.querySelector("#tableData"), //loadin覆盖的dom元素节点
      });
      uploadFile(data).then((res) => {
        loading.close()
        if (res.code !== 200) {

          this.$message({
            message: res.msg,
            type: "error",
          });
        } else {
          this.obj.data = res.data
          this.archivesCountData.forEach(item => {
            item.fileName.forEach(i => {
              if (i.name == this.obj.name.name) {
                i.url = this.obj.data
              }
            })
          })
          this.$forceUpdate();
          // this.dz.push(this.obj)
          this.obj = {}
          this.$message({
            message: res.msg,
            type: "success",
          });

        }
        this.fileList = [];
      });
    },
    getexpand(row) {
      this.liu.dirName = row.dir
    },
    checkFile(row, item) {
      this.obj.ythId = item.ythId
      this.obj.name = item.name
      this.obj.dir = row.dir
      this.$confirm("上传文件是否为 数字化件?", "提示", {
        confirmButtonText: "是",
        cancelButtonText: "否",
        type: "warning",
      }).then(() => {
        this.obj.fileType = 1
      }).catch(() => {
        this.obj.fileType = 0
      }).finally(() => {
        this.$refs.upload.$refs['upload-inner'].handleClick()
      })
    },
    submitUpload() {
      itemBusinessApi.getUploadUrl(this.liu).then(res => {
        if (res.code === 200) {
          let formData = new FormData();
          formData.append("path", res.data);
          formData.append("file", this.fileList[0].raw);
          formData.append("sblsh", this.liu.sblsh);
          formData.append("ythId", this.obj.ythId);
          formData.append("fileType", this.obj.fileType);
          this.getUploadFile(formData)
        }
      })
    },
    fileChange(file, fileList) {
      this.fileList = fileList;
      let fileName = file.name;
      let uid = file.uid
      let pos = fileName.lastIndexOf(".");
      let lastName = fileName.substring(pos, fileName.length);
      if (
          lastName.toLowerCase() !== ".doc" &&
          lastName.toLowerCase() !== ".docx" &&
          lastName.toLowerCase() !== ".xls" &&
          lastName.toLowerCase() !== ".xlsx" &&
          lastName.toLowerCase() !== ".pdf" &&
          lastName.toLowerCase() !== ".png" &&
          lastName.toLowerCase() !== ".jpg"
      ) {
        this.$message.error("文件必须为.doc .docx .xls .xlsx .pdf .png jpg 类型");
        // this.resetCompressData()
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].uid == uid) {
            fileList.splice(i, 1)
          }
        }
        this.fileList = [];
        return;
      }
      // 限制上传文件的大小35MB,不得小于0KB
      const isLt =
          file.size / 1024 / 1024 < 35 && file.size / 1024 / 1024 > 0;
      if (!isLt) {
        this.$message.error("上传文件大小不得小于0KB,不得大于35MB!");
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].uid == uid) {
            fileList.splice(i, 1)
          }
        }
        this.fileList = [];
      }
      let existFile = fileList.slice(0, fileList.length - 1).find(f => f.name === file.name)//如果文件名重复
      if (existFile) {
        this.$message.error('当前文件已经存在!');
        fileList.pop()
      }
      this.fileList = fileList
    },
    deleteFie(row, item) {
      let a = row
      let obj = {}
      obj.path = [item.url]
      obj.sblsh = this.liu.sblsh
      var formData = new FormData();
      formData.set("path", obj.path);
      formData.set("sblsh", this.liu.sblsh)
      removeFile(formData).then(res => {
        if (res.code == 200) {
          a.fileName.forEach(items => {
            if (items.name == item.name) {
              items.url = undefined
            }
          })
          row = a
          this.$forceUpdate();
          this.$message({
            message: res.msg,
            type: "success",
          });
        }
      })
    },
    close() {
      this.archivesCountData = []
    },
    tableClick(row) {
      this.$refs.tables.toggleRowExpansion(row);
      this.liu.dirName = row.dir
    },
    handleClick(tab, event) {
      this.activeName = tab.name;
    },
    handleTransferZip(idnex, row) {
      this.$confirm("此操作为Zip包移交, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const loading = this.$loading({
            lock: true, //lock的修改符--默认是false
            text:"正在加载数据中...",
            target: document.querySelector("#tableData"), //loadin覆盖的dom元素节点
          });

          itemBusinessApi
            .transferZip({ sblsh: row })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.msg);
                this.getData();
              } else {
                this.$message.error(res.msg);
              }
              loading.close();
            })
            .catch(() => {
              this.$message.error("移交失败,系统异常");
              loading.close();
            });
        })
        .catch(() => {
        });
    },
    handleTest(index, row) {
      getResults({
        'sblsh': row.sblsh
      }).then((res) => {
        if (res.code == 200) {
          this.fourTestTableData = res.data
          this.fourTestDialog.title = "检测结果";
          this.fourTestDialog.dialogVisible = true;
        }else {
          this.$message.warning(res.msg)
        }
      });

    },
    cancelFourTest() {
      this.fourTestDialog.dialogVisible = false;
    },
    //生成信息包
    handleCreateZip(idnex, row) {
      this.$confirm("此操作为Zip包生成, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const loading = this.$loading({
            lock: true, //lock的修改符--默认是false
            text:"正在加载数据中...",
            target: document.querySelector("#tableData"), //loadin覆盖的dom元素节点
          });
          // TODO
          itemBusinessApi
            .createYGDZip({ sblsh: row.sblsh })
            .then(res => {
              loading.close();
              if (res.code === 200) {
                this.$message.success(res.msg)
              } else {
                this.$message.error(res.msg)
              }
            })
        })
        .catch((res) => {
          loading.close();
        });

    },
    parseTime(val) {
      if (!val) {
        return "";
      }
      let time = parseInt(val);
      return tool.parseTime(time);
    },
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      let that = this;
      // 一定要使用 nextTick 来改变height 不然不会起作用
      //nav 45;
      //padding:10
      this.$nextTick(() => {
        let tempHeight = height - 45 - 100 - 51;
        let treeDemoHeight = tempHeight + 30;
        that.$refs.tables.style = "height:" + treeDemoHeight + "px";

        this.tableHeight = tempHeight;
      });
    },
    filterColumn(value, row, column) {
    },
    handleManagement(index, row) {
      this.$router.push({ path: "./first", query: { id: row } });
      localStorage.setItem("sblsh", row.sblsh);
    },
    handleArchivesCountResult(index, row) {
      this.archivesCountData = []
      this.liu.sblsh = row.sblsh
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text:"正在加载数据中...",
        target: document.querySelector("#tableData"), //loadin覆盖的dom元素节点
      });
      itemBusinessApi.getFileUrl({ sblsh: row.sblsh }).then(res1 => {
        if (res1.code === 200) {
          itemBusinessApi.xmlFiles({ fileUrl: res1.data }).then(res2 => {
            if (res2.code === 200) {
              itemBusinessApi.archivesCount({ sblsh: row.sblsh, xmlFiles: JSON.stringify(res2.data) }).then(res3 => {
                if (res3.code === 200) {
                  loading.close();
                  // this.archivesCountData = res3.data;
                  res3.data.forEach(item => {
                    let obj = {}
                    obj.dir = item.dir
                    obj.fileName = []
                    obj.ythId = '';
                    item.fileName.forEach(i => {
                      let a = {}
                      a.name = i.materialName
                      a.ythId = i.ythId;
                      obj.fileName.push(a)
                    })
                    this.archivesCountData.push(obj)
                  })
                  this.isCreateZip = true;
                  this.archivesCountDialog.dialogVisible = true;
                  this.getData();
                }
              })
            }
          })
        }
      })


    },
    search() {
      this.query.pageNo = 0;
      this.findDataStatistics();
      this.getData();
    },
    nodeClick(data,e) {
      localStorage.setItem('data', JSON.stringify(data))
      this.query.pageNo = 0;
      this.query.sblsh = null;
      this.query.type = data.type;

      //部门
      if (data.type == 0) {
        this.query.bmbm = data.id;
        this.query.name = data.name;
        this.query.itemCode = null;
        this.query.itemName = null;
        this.query.labelOne = "部门编码";
        this.query.labelTwo = "部门名称";
      }
      //事项
      if (data.type == 1) {
        this.query.bmbm = null;
        this.query.name = null;
        this.query.itemCode = data.id;
        this.query.itemName = data.name;
        this.query.labelOne = "事项编码";
        this.query.labelTwo = "事项名称";
      }
      this.findDataStatistics();
      this.getData();
    },
    handleView(index, row) {
      this.dialog.title = "查看";
      this.ruleForm = row;
      this.logQuery.sblsh = row.Sblsh;
      this.dialog.dialogVisible = true;
      //   this.getBaseData(row.Sblsh);
    },
    handleClose(done) {
      // this.archivesCountData = []
      // this.form = {};
      // done();
    },
    cancel() {
      this.form = {};
      this.dialog.dialogVisible = false;
    },
    getBaseData(val) {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      Promise.all([
        new Promise((re, rp) => {
          itemBusinessApi.getItemSLEntity({ sblsh: val }).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          logApi.page2(this.logQuery).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          itemBusinessApi.getFileBySblshAllNoPage(this.logQuery).then((a) => {
            re(a);
          });
        }),
        new Promise((re, rp) => {
          materialsBillApi.getMaterialBillTree({ sblsh: val }).then((a) => {
            re(a);
          });
        }),
      ])
        .then((a) => {
          this.baseForm = a[0].data;
          this.baseTableData = a[1].data.content;
          this.matterialTableData = a[2].data;
          this.treeData = a[3].data;
          this.dialog.dialogVisible = true;
          // this.pass=true;
          loading.close();
        })
        .catch((s) => {
          loading.close();
        });
    },
    save(formName) {
      // this.$refs[formName].validate((valid) => {
      //   if (valid) {
      //     itemBusinessApi.save(this.ruleForm).then((a) => {
      //       if (a.code == 200) {
      this.$message.success("保存成功");
      this.query.pageNo = 0;
      this.cancel();
    },
    getData() {
      if (this.query.archivesNo == "") {
        this.query.archivesNo = null;
      }
      if (this.query.sblsh == "") {
        this.query.sblsh = null;
      }
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text:"正在加载数据中...",
        target: document.querySelector("#tableData"), //loadin覆盖的dom元素节点
      });
      itemBusinessApi.getItemSlDataForMetaData(this.query).then((a) => {
        this.tableData = [];
        if (a.code == 200) {
          a.data.forEach((f) => {
          /*  let businessStatus = 0;
            let tempName = "";
            if (f.name.lastIndexOf("年") > 0) {
              tempName = f.name.replaceAll("年", "");
            } else if (f.name.lastIndexOf("永久") > 0) {
              tempName = f.name.replaceAll("永久", "");
            }
            if (f.itemSl) {
              businessStatus = 0;
            }
            if (f.itemGc) {
              businessStatus = 1;
            }
            if (f.itemBj) {
              businessStatus = 2;
            }
            if (f.itemLq) {
              businessStatus = 3;
            }
            f.businessStatus = businessStatus;*/
            this.tableData.push(f);
          });
          this.query.total = a.totalCount;
          loading.close()
        }
      });
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.$refs.tables.bodyWrapper.scrollTop = 0
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.$refs.tables.bodyWrapper.scrollTop = 0
      // this.query.pageSize = val;
      this.getData();
    },
    // 这个方法用来动态设置 height
    getAutoHeight() {
      let el = document.querySelector("#tableData"),
          elParent = el.parentNode,
          pt = this.getStyle(elParent, "paddingTop"),
          pb = this.getStyle(elParent, "paddingBottom");
      // 一定要使用 nextTick 来改变height 不然不会起作用
      this.$nextTick(() => {
        this.height = elParent.clientHeight - (pt + pb) + "px";
      });
    },
    // 获取样式 我们需要减掉 padding-top， padding-bottom的值
    getStyle(obj, attr) {
      // 兼容IE浏览器
      let result = obj.currentStyle
          ? obj.currentStyle[attr].replace("px", "")
          : document.defaultView
              .getComputedStyle(obj, null)[attr].replace("px", "");
      return Number(result);
    }
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}


.down-tree {
  height: 700px;
  display: block;
  /* overflow-y: scroll; */
}

.mote {
  display: flex;
  height: 100%;
  box-sizing: border-box;
}
.searchDiv {
  margin-top: 10px;
}

.countDiv {
  display: flex;
  justify-content: space-around;
  align-items: center;
  /* font-weight: 1000; */
  color: mistyrose;
  /* text-align: center; */
  font-family: Verdana;
  font-size: 14px;
  font-weight: bold;
  color: red;
  font: 14px "微软雅黑";
  /*设置字体和字体大小*/
  margin: 0px auto;
  /*设置元素外边距*/
  font-weight: 500;
  /*设置字体粗细*/
  text-align: center;
  /*设置文字居中*/
  color: #f35626;
  margin-bottom: 5px;
  /*设置文字颜色*/
  /*-webkit-animation:bounce 2s infinite;!*设置动画*!*/
}

/*@-webkit-keyframes bounce{!*创建动画*!
  0%,100%,20%,50%,80%{
    -webkit-transform:translateY(0);
  }40%{
     -webkit-transform:translateY(-30px);
   }60%{
      -webkit-transform:translateY(-15px);
    }
}*/
/deep/ .readyElCard .el-card__body{
  padding: 0 0 0 10px;
}

/deep/ .resultDialog {
  min-width: 650px !important;
}

/deep/ .resultTable .el-table__header-wrapper tr th{
  background-color: #F7FBFF;
}
/deep/ .resultTable .el-table__body-wrapper tr{
  height: 50px !important;
}
.el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: auto;
}
.readyFileCard{
  width: 80%;
  margin-top: 10px;
  margin-left: 10px;
}
.tableDiv{
  min-width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  /*height: calc(100% - 0px);*/
}
</style>

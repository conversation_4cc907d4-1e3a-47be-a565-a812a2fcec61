<template>
  <!-- 期限 -->
  <div class="divBox">
    <div class="searchDiv1">
      <el-form
          :inline="true"
          :model="query"
          class="demo-form-inline"
          size="small"
      >
        <el-form-item label="密级名称">
          <el-input v-model="query.name" placeholder="密级名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" plain @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" class="tableClass" :height="tableHeight" :header-cell-style="{ background: '#F6F7FA' }">
      <el-table-column
          prop="name"
          label="密级名称"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="createDate"
          label="创建时间"
          align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.$index, scope.row)"
          >编辑
          </el-button
          >
          <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleRemove(scope.$index, scope.row)"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination  v-model:page-size="query.pageSize" :page-sizes="[10, 20, 50, 100]"
      :small="small" :disabled="disabled" :background="background" layout="total, sizes, prev, pager, next, jumper"
      :total="query.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" style="float: right;margin-top: 10px;"/>
    <!-- <el-pagination 
                    @size-change="handleSizeChange" 
                    @current-change="handleCurrentChange" 
                    :current-page.sync="query.pageNoTemp" 
                    :page-size="query.pageSize"
                    layout="total, prev, pager, next"
                    :total="query.total">
                </el-pagination> -->
    <el-dialog
        :title="dialog.title"
        :visible.sync="dialog.show"
        width="30%"
        :before-close="handleClose"
        center
    >
      <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
      >
        <el-form-item label="密级名称" prop="name">
          <el-input v-model="ruleForm.name"></el-input>
        </el-form-item>
        <el-form-item label="密级代码" prop="code">
          <el-input v-model="ruleForm.code"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {classTypeApi} from "../api/classType";

export default {
  data() {
    return {
      query: {
        pageNo: 0,
        pageSize: 100,
        total:0
      },
      tableHeight: 730,
      dialog: {
        title: "新增",
        show: false,
      },
      rules: {
        name: [
          {required: true, message: "密级名称不能为空", trigger: "blur"},
        ],
        code: [
          {required: true, message: "密级代码不能为空", trigger: "blur"},
        ],
      },
      ruleForm: {},
      tableData: [],
    };
  },
  created() {
    this.getData();
  },
  mounted() {
    this.initTableHeight()
  },
  methods: {
    //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      // 一定要使用 nextTick 来改变height 不然不会起作用
      //nav 45;
      //padding:10
      this.$nextTick(() => {
        let tempHeight = height - 45 - 50 - 51 ; //+ 32
        this.tableHeight = tempHeight;

        console.log(this.tableHeight, "height");
      });
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;

      this.query.pageNoTemp = 1;

      this.query.pageNo = 0;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNoTemp = val;

      this.query.pageNo = val - 1;
      this.getData();
    },
    close() {
      this.dialog.show = false;
      this.ruleForm = {};
    },
    handleEdit(index, row) {
      this.dialog.title = "编辑";
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.dialog.show = true;
    },
    handleAdd() {
      this.dialog.title = "新增";
      this.ruleForm = {};
      this.$nextTick(()=>{
        this.$refs.ruleForm.clearValidate();
      })
      this.dialog.show = true;
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          classTypeApi.save(this.ruleForm).then((a) => {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
            this.query.pageNo = 0;
            this.getData();
            this.ruleForm = {};
            this.dialog.show = false;
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            classTypeApi.remove({ID: row.id}).then((a) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.query.pageNo = 0;
              this.getData();
            });
          })
          .catch(() => {
          });
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    getData() {
      classTypeApi.page(this.query).then((a) => {
        this.tableData = a.data;
        this.query.total = a.totalCount
      });
    },
  },
};
</script>
<style scoped>
.divBox{
  padding: 15px;
  box-sizing: border-box;
}
.tableClass {
  width: 100%;
  /* padding: 0 20px; */
}
</style>
<template>
  <!-- 部门水印 -->
  <div class="divBox">
    <div class="searchDiv">
      <el-form
          :inline="true"
          :model="query"
          class="demo-form-inline"
          size="small"
      >
        <el-form-item>
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" class="tableClass" :height="tableHeight" border>
      <el-table-column
          prop="departments.name"
          label="部门名称"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="dzyzAccount"
          label="电子印章账号"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="outUserId"
          label="用户ID"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="createDate"
          label="创建时间"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="updateDate"
          label="修改时间"
          align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.$index, scope.row)"
          >编辑
          </el-button
          >
          <!--          <el-button
                        type="text"
                        icon="el-icon-delete"
                        @click="handleRemove(scope.$index, scope.row)"
                    >删除</el-button
                    >-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="query.pageNoTemp"
        :page-size="query.pageSize"
        layout="total, prev, pager, next"
        :total="total">
    </el-pagination>
    <el-dialog
        :title="dialog.title"
        :visible.sync="dialog.show"
        width="50%"
        :before-close="handleClose"
        center
    >
      <el-row>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="120px"
            class="demo-ruleForm"
        >
          <el-col :span="24">
            <el-form-item label="部门名称" prop="unitsId">
              <el-select v-model="ruleForm.unitsId" filterable placeholder="请选择部门">
                <el-option
                    v-for="item in departments"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="电子印章账号" prop="dzyzAccount">
              <el-input v-model="ruleForm.dzyzAccount" placeholder="请输入电子印章账号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="用户ID" prop="outUserId">
              <el-input v-model="ruleForm.outUserId" placeholder="请输入用户ID"></el-input>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="24">
                      <el-form-item label="横距" prop="">
                        <el-slider
                            v-model="ruleForm.x"
                            show-input>
                        </el-slider>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="纵距" prop="">
                        <el-slider
                            v-model="ruleForm.y"
                            show-input>
                        </el-slider>
                      </el-form-item>
                    </el-col>-->
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {page, departments, save, remove} from "../api/departmentSignature";

export default {
  data() {
    return {
      query: {
        pageNo: 0,
        pageSize: 100,
      },
      total: 0,
      tableHeight: 730,
      units: [],
      dialog: {
        title: "新增",
        show: false,
      },
      rules: {
        unitsId: [
          {required: true, message: "部门不能为空", trigger: "blur"},
        ],
        dzyzAccount: [
          {required: true, message: "电子印章账号不能为空", trigger: "blur"},
        ],
        outUserId: [
          {required: true, message: "用户ID不能为空", trigger: "blur"},
        ],
      },
      ruleForm: {},
      tableData: [],
      departments: [],
    };
  },
  created() {
    this.getData();
  },
  mounted() {
    this.initTableHeight()
  },
  methods: {
    //获取基础数据
    getBaseData() {
      Promise.all([
        new Promise((re, rp) => {
          departments().then(res => {
            re(res.data)
          })
        })
      ]).then(res => {
        this.departments = res[0];
      })
    },
    //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      // 一定要使用 nextTick 来改变height 不然不会起作用
      //nav 45;
      //padding:10
      this.$nextTick(() => {
        let tempHeight = height - 45 - 10 - 51;
        this.tableHeight = tempHeight;
      });
    },
    handleSizeChange(val) {
      this.query.pageSize = val;

      this.query.pageNoTemp = 1;

      this.query.pageNo = 0;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNoTemp = val;
      this.query.pageNo = val - 1;
      this.getData();
    },
    close() {
      this.dialog.show = false;
      this.ruleForm = {};
    },
    handleEdit(index, row) {
      this.getBaseData()
      this.dialog.title = "编辑";
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.dialog.show = true;
    },
    handleAdd() {
      this.ruleForm = {}
      this.getBaseData()
      this.dialog.title = "新增";
      this.dialog.show = true;
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          save(this.ruleForm).then((res) => {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
            this.query.pageNo = 0;
            this.getData();
            this.ruleForm = {};
            this.dialog.show = false;
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        console.log(row)
        remove({id: row.id}).then((a) => {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.query.pageNo = 0;
          this.getData();
        });
      })
          .catch(() => {
          });
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    getData() {
      page(this.query).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.content
          this.total = res.data.totalElements
        }
      })
    },
  },
};
</script>
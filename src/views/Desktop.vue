<template>
    <div class="desktop">
        <div class="title first" >
            dasdas
        </div>
    </div>
</template>
<style scoped>
.desktop .title{
    width: 290px;
    border: 1px solid red;
    height: 190px;
}
.desktop .title .first{
    /* background: url(); */
}
</style>
<script>
export default {
    data(){
        return{
            backgroundFirst: {
                backgroundImage: 'url(' + require('../assets/img/组1.png') + ')'
            }
        }
    },
    created(){
 
    },
}
</script>
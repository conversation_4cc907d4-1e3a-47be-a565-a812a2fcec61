<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="first"/>
      <el-tab-pane label="流程信息" name="second">
        <div class="title">受理信息</div>
        <SLForm :form="SLForm"></SLForm>
        <div class="title">过程信息</div>
        <GCForm :form="GCForm"></GCForm>
        <div class="title">办结信息</div>
        <BJForm :form="BJForm"/>
        <!--        <div class="title">出证信息</div>
        <LQForm :form="LQForm" />-->
      </el-tab-pane>
      <el-tab-pane label="归档信息" name="third"></el-tab-pane>
      <el-tab-pane label="电子文件信息" name="fourth"></el-tab-pane>
      <el-tab-pane label="返回" name="return"></el-tab-pane>
    </el-tabs>
    <el-row :gutter="20">
      <el-form label-width="200px">
        <div v-if="this.GCForm.Custom">
          <el-col :span="24">
            <p class="text">自定义</p>
          </el-col>
          <div v-for="(item, index) in GCForm.Custom" :key="index">
            <el-col :span="8">
              <el-form-item :label="item.note">
                <el-input v-model="item.settingValues"></el-input>
              </el-form-item>
            </el-col>
          </div>
        </div>
      </el-form>
    </el-row>
    <span slot="footer" class="dialog-footer">
<!--      <el-button type="primary" @click="save" v-if="this.GCForm.Custom != null">保存</el-button>-->
      <!--      <el-button @click="cancel">取 消</el-button>-->
      <!-- <el-button @click="tui">返回</el-button> -->
    </span>
  </div>
</template>
<script>
import SLForm from "../components/SLForm.vue";
import GCForm from "../components/GCForm.vue";
import BJForm from "../components/BJForm.vue";
import LQForm from "../components/LQForm.vue";
import GDForm from "@/components/GDForm.vue";
import {getGcData, getBjData, getCzData, addCustomStting, getSLData} from "../api/first";
import BasicForm from "@/components/BasicForm";

export default {
  components: {
    BasicForm,
    SLForm,
    GCForm,
    BJForm,
    LQForm,
    GDForm,
  },
  data() {
    return {
      activeName: "second",
      SLForm: {},
      GCForm: {},
      BJForm: {},
      GDForm: {},
    };
  },
  methods: {
    getDate(row) {
      例子
      // 比如需要这样的格式 yyyy-MM-dd hh:mm:ss
      var date = new Date(row);
      Y = date.getFullYear() + '-';
      M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      D = date.getDate() + ' ';
      h = date.getHours() + ':';
      m = date.getMinutes() + ':';
      s = date.getSeconds();
      console.log(Y + M + D + h + m + s);
      return Y + M + D + h + m + s
    },
    getData() {
      getSLData({sblsh: localStorage.getItem("sblsh"), name: "gc"}).then(
          (res) => {
            if (res.code == 200) {
              this.SLForm = res.data;
            }
          }
      );
      getGcData({sblsh: localStorage.getItem("sblsh"), name: "gc"}).then(
          (res) => {
            if (res.code == 200) {
              this.GCForm = res.data;
            }
          }
      );
      getBjData({sblsh: localStorage.getItem("sblsh")}).then((res) => {
        if (res.code == 200) {
          this.BJForm = res.data;
          // this.BJForm.resultdate = getDate(res.data.resultdate)
        }
      });
      getCzData({sblsh: localStorage.getItem("sblsh")}).then((res) => {
        // console.log(res, 333);
      });
    },

    handleClick(tab, event) {
      switch (tab.name) {
        case "second":
          this.activeName = tab.name
          this.$router.push({
            path: "./second",
            query: {sblsh: localStorage.getItem("sblsh")},
          });
          break;
        case "third":
          this.activeName = tab.name
          this.$router.push({
            path: "./third",
            query: {sblsh: localStorage.getItem("sblsh")},
          });
          break;
        case "fourth":
          this.activeName = tab.name
          this.$router.push({
            path: "./fourth",
            query: {sblsh: localStorage.getItem("sblsh")},
          });
          break;
        case "first":
          this.activeName = tab.name
          this.$router.push({
            path: "./first",
            query: {sblsh: localStorage.getItem("sblsh")},
          });
          break;
        case "return":
          this.activeName = tab.name
          this.$router.push({
            path: "./metadataManagement",
            // query: { sblsh: localStorage.getItem("sblsh") },
          });
          break;
      }
    },
    tui() {
      this.$router.push({path: "./metadataManagement"});
      // this.$router.back()
    },
    save() {
      console.log(this.GCForm, "333", localStorage.getItem("sblsh"));
      this.GCForm.sblsh = localStorage.getItem("sblsh");
      addCustomStting(this.GCForm).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: "保存成功",
            type: "success",
          });
        } else {
          this.$message({
            message: "保存失败",
            type: "warning",
          });
        }
      });
    },
    cancel() {
    },
  },
  mounted() {
    console.log(this.GCForm, 999)
  },
};
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}

.title {
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
  padding-bottom: 10px;
  font-size: 16px;
  margin-left: 5px;
}

.text {
  font-size: 18px;
  margin-left: 60px;
  color: red;
}

::v-deep #tab-return {
  margin-left: 1200px;
  /* border: #3a8ee6 solid 1px; */
  padding: 5px 15px;
  align-content: center;
  margin-top: 2px;
  border-radius: 5px;
  text-align: center;
  line-height: 20px;
  font-size: medium;
}
</style>

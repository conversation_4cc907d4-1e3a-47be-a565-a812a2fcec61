<template>
  <div class="divBox">
    <div class="searchDiv">
      <el-form
        :inline="true"
        :model="query"
        class="demo-form-inline"
        size="small"
      >
        <el-form-item label="检测类型">
          <el-input v-model="query.id" placeholder="检测类型"></el-input>
        </el-form-item>
        <el-form-item label="检测分类">
          <el-input
            v-model="query.namingRule"
            placeholder="检测分类"
          ></el-input>
        </el-form-item>
        <el-form-item label="检测项名称">
          <el-input
            v-model="query.namingRule"
            placeholder="检测项名称"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" class="tableClass" :height="tableHeight" border>
      <el-table-column label="序号" align="center">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>

      <!-- <el-table-column prop="is_send" label="检测类型" align="center">
        <template slot-scope="scope">
          <samp v-if="scope.row.is_send == 0">不发送</samp>
          <samp v-if="scope.row.is_send == 1">发送</samp>
        </template>
      </el-table-column> -->
      <el-table-column
        prop="check_name"
        label="检测项名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="list_four"
        label="检测分类（四性）"
        align="center"
      ></el-table-column>

      <el-table-column prop="is_open" label="是否开启" align="center">
        <template slot-scope="scope">
          <samp v-if="scope.row.is_open == 0">是</samp>
          <samp v-if="scope.row.is_open == 1">否</samp>
        </template>
      </el-table-column>
      <el-table-column
        prop="extension"
        label="检测描述"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="goal"
        label="目的"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleRemove(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="query.pageNoTemp"
      :page-size="query.pageSize"
      layout="total, prev, pager, next"
      :total="query.total"
    >
    </el-pagination> -->
    <el-pagination v-model:currentPage="currentPage4" v-model:page-size="pageSize4" :page-sizes="[10, 20, 50, 100]"
          :small="small" :disabled="disabled" :background="background" layout="total, sizes, prev, pager, next, jumper"
          :total="query.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    <!-- 规则编辑 -->
    <el-dialog
      :title="dialog.title"
      width="40%"
      :visible.sync="dialog.dialogVisible"
      :before-close="handleClose2"
      center
    >
      <div style="display: flex">
        <el-form
          :model="ruleForm"
          status-icon
          :rules="rules"
          ref="ruleForm"
          size="small"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="检测项名称" prop="check_name">
            <el-col :span="20">
              <el-input
                v-model="ruleForm.check_name"
                placeholder="请输入检测项名字"
              ></el-input>
              <!-- <el-button type="primary" @click="fouch_click">选择分类</el-button> -->
            </el-col>
          </el-form-item>
          <el-form-item label="是否开启" prop="is_open">
            <el-col :span="20">
              <el-radio-group v-model="ruleForm.is_open">
                <el-radio :label="0">是</el-radio>
                <el-radio :label="1">否</el-radio>
              </el-radio-group>
            </el-col>
          </el-form-item>
          <el-form-item label="检测描述" prop="extension">
            <el-col :span="20">
              <el-input
                type="textarea"
                v-model="ruleForm.extension"
                placeholder="请输入检测描述"
              ></el-input>
            </el-col>
          </el-form-item>
          <el-form-item label="目的" prop="checkOrder">
            <el-col :span="20">
              <el-input
                type="textarea"
                v-model="ruleForm.goal"
                placeholder="请输入目的"
              ></el-input>
            </el-col>
          </el-form-item>
        </el-form>
        <el-form style="display: flex; height: 300px; overflow-y: scroll">
          <div style="padding-top: 5px">检测分类</div>
          <el-tree
            @check="changeSelect"
            :data="treeData"
            style="width: 240px"
            :props="defaultProps"
            @node-click="handleNodeClick"
            show-checkbox
            check-on-click-node
            ref="trees"
            node-key="id"
          ></el-tree>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="cancel('ruleForm')">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-tree
        @check="changeSelect"
        :data="treeData"
        :props="defaultProps"
        @node-click="handleNodeClick"
        show-checkbox
        check-on-click-node
        ref="trees"
        node-key="id"
      ></el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import itemCode from "../components/ItemCode.vue";
import { departApi } from "../api/department";
import {
  FourChecks,
  putFourCheckSetting,
  getFourCheckSetting,
  deleteFourCheck,
  updateFourCheck,
} from "../api/first";
export default {
  components: {
    itemCode,
  },
  data() {
    return {
      tableHeight: 730,
      bmbm: null,
      bmName: null,
      query: {
        pageNo: 0,
        pageSize: 10,
        total: 0,
      },
      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      rules: {
        check_name: [
          { required: true, message: "请输入检测项名称", trigger: "blur" },
        ],
        is_open: [
          { required: true, message: "请选择是否开启", trigger: "change" },
        ],
        is_send: [
          { required: true, message: "请选择检测类型", trigger: "change" },
        ],
        checkClass: [
          { required: true, message: "请选择检测分类", trigger: "change" },
        ],
        four_setting_name: [
          { required: true, message: "请选择检测分类", trigger: "change" },
        ],
      },
      ruleForm: {},
      tableData: [],
      dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      treeData: [],
      checkedNodes: [],
      defaultList: [],
    };
  },
  created() {
    this.getdata();
  },
  mounted() {
    this.initTableHeight();
  },
  
  methods: {
    fouch_click() {
      this.dialogVisible = true;
    },
    submit() {
      this.dialogVisible = false;
      this.$refs.trees.setCheckedKeys([]);
    },
    changeSelect(data, check) {
      console.log(data, check);
      this.checkedNodes = [];
      check.checkedNodes.forEach((item) => {
        if (item.id) {
          this.checkedNodes.push(item.id);
        }
      });
    },
    handleClose2() {
      this.ruleForm = {};
      this.$refs.ruleForm.resetFields();
      this.dialog.dialogVisible = false;
      this.checkedNodes = [];
      // this.reset()
      // 清空树型选中
      this.$refs.trees.setCheckedKeys([]);
    },
    handleNodeClick(data) {
      let arr = this.$refs.trees.getCheckedNodes();
    },
    handleClose(done) {
      this.dialogVisible = false;
      this.checkedNodes = [];
      //清空树型数据
      this.$refs.trees.setCheckedKeys([]);
    },
    //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      // 一定要使用 nextTick 来改变height 不然不会起作用
      //nav 45;
      //padding:10
      this.$nextTick(() => {
        let tempHeight = height - 45 - 10 - 51;
        this.tableHeight = tempHeight;
      });
      getFourCheckSetting().then((res) => {
        this.tableData = res.data;
        this.tableData.forEach((item) => {
          item.list_four.join(",");
        });
      });
    },
    // 获取树型数据
    getdata() {
      FourChecks().then((res) => {
        let intactData = { label: "", children: [] };
        let realData = { label: "", children: [] };
        let usableData = { label: "", children: [] };
        let safetyData = { label: "", children: [] };
        let intact = [];
        let real = [];
        let safety = [];
        let usable = [];
        res.data.intact.forEach((item) => {
          intactData.label = item.four_check;
          let obj = { label: item.check_options, id: item.id };
          intactData.children.push(obj);
        });
        res.data.real.forEach((item) => {
          realData.label = item.four_check;
          let obj = { label: item.check_options, id: item.id };
          realData.children.push(obj);
        });
        res.data.safety.forEach((item) => {
          safetyData.label = item.four_check;
          let obj = { label: item.check_options, id: item.id };
          safetyData.children.push(obj);
        });
        res.data.usable.forEach((item) => {
          usableData.label = item.four_check;
          let obj = { label: item.check_options, id: item.id };
          usableData.children.push(obj);
        });
        intact.push(intactData);
        real.push(realData);
        safety.push(safetyData);
        usable.push(usableData);
        let data = intact.concat(real, safety, usable);
        // console.log(data)
        this.treeData = data;
      });
    },
    handleAdd() {
      this.dialog.title = "添加";
      this.dialog.dialogVisible = true;
    },
    search() {
      this.query.pageNo = 0;
      this.getdata();
    },
    handleEdit(index, row) {
      this.defaultList = row.four_setting_name.split(",");
      this.dialog.title = "编辑";
      this.ruleForm = row;
        this.$nextTick(() => {
          this.$refs.trees.setCheckedKeys(this.defaultList);
        });
      this.dialog.dialogVisible = true;
    },
    handleSync() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      departApi
        .syncData({ bmbm: this.ruleForm.bmbm, name: this.ruleForm.name })
        .then((a) => {
          this.form = a.data[0];
          loading.close();
        })
        .catch((a) => {
          loading.close();
        });
    },

    handleViewItemCode(index, row) {
      this.bmName = row.name;
      this.bmbm = row.bmbm;
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteFourCheck({ id: row.id }).then((a) => {
            if (a.code == 200) {
              this.$message.success("删除成功");
              this.query.pageNo = 0;
              // this.getData();
              this.initTableHeight();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => {});
    },
    cancel(ruleForm) {
      this.form = {};
      this.ruleForm = {};
      this.dialog.dialogVisible = false;
      this.$refs["ruleForm"].resetFields();
    },
    save(ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (valid) {
          if (this.dialog.title == "编辑") {
            updateFourCheck(this.ruleForm).then((res) => {
              console.log(res);
              if (res.code == 200) {
                // this.dialog.dialogVisible = false;
                this.$message({
                  message: "修改成功",
                  type: "success",
                });
                this.getdata();
                this.dialog.dialogVisible = false;
                this.$refs.ruleForm.resetFields();
                this.initTableHeight();
              } else {
                this.$message({
                  message: "修改失败",
                  type: "warning",
                });
                this.dialog.dialogVisible = false;
                this.$refs.ruleForm.resetFields();
              }
            });
          } else {
            this.ruleForm.four_setting_id = this.checkedNodes;
            putFourCheckSetting(this.ruleForm).then((a) => {
              // console.log(a);
              if (a.code == 200) {
                this.$message.success("保存成功");
                this.checkedNodes = [];
                // this.reset()
                // 清空树型选中
                this.$refs.trees.setCheckedKeys([]);
                this.query.pageNo = 0;
                this.cancel();
                // this.getData();
                this.initTableHeight();
                this.$refs.ruleForm.resetFields();
              } else {
                this.$message.error("保存失败");
                this.checkedNodes = [];
                this.$refs.ruleForm.resetFields();
              }
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.query.pageNoTemp = val;
      this.getData();
    },
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}
.searchDiv {
  margin-top: 10px;
}
</style>

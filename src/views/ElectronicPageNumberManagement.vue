<template>
  <div class="divBox" style="padding-top: 10px">
    <el-row
      :gutter="24"
      style="margin-left: 0px; margin-right: 0px; padding: 0px"
    >
      <el-col :span="24">
        <div class="searchDiv1">
          <el-form
            :inline="true"
            :model="query"
            class="demo-form-inline"
            size="small"
          >
            <!-- <el-form-item :label="query.font">
              <el-input v-model="query.font" placeholder="页码字体"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" v-if="" @click="handleAdd">添加</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table
          :data="tableData"
          class="tableClass"
          :height="tableHeight"
          :header-cell-style="{ background: '#F6F7FA' }"
        >
          <el-table-column prop="taskCode" label="序号" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            prop="fontName"
            label="页码字体"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="fontSize"
            label="字体大小"
            align="center"
          ></el-table-column>
          <el-table-column prop="isUse" label="是否启用" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isUse == 0">启用</span>
              <span v-else>关闭</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-edit"
                @click="handleEdit(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                type="text"
                icon="el-icon-delete"
                v-if="scope.row.isUse == '1'"
                @click="handleRemove(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog
      width="30%"
      :title="dialog.title"
      :visible.sync="dialog.dialogVisible"
      :before-close="handleClose"
      center
      :append-to-body="true"
    >
      <el-form
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="ruleForm"
        size="small"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="使用字体" prop="fontName">
          <el-col :span="20">
            <el-select
              v-model="ruleForm.fontName"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in fontData"
                :key="index"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-form-item>

        <el-form-item label="字体大小" prop="fontSize">
          <el-col :span="20">
            <el-input-number v-model="ruleForm.fontSize"  :min="1" :max="20" label="字体大小"></el-input-number>
          </el-col>
        </el-form-item>

        <!-- <el-form-item label="页码位置" prop="ruleForm.location">
          <el-col :span="12">
            <el-select v-model="ruleForm.location" placeholder="">
              <el-option label="上居左"></el-option>
              <el-option label="上居中"></el-option>
              <el-option label="上居右"></el-option>
              <el-option label="下居左"></el-option>
              <el-option label="下居中"></el-option>
              <el-option label="下居右"></el-option>
            </el-select>
          </el-col>
        </el-form-item> -->

        <!-- <el-form-item label="页码是否正反面">
          <el-col :span="12">
            <el-radio v-model="ruleForm.isZF" label="0">正面</el-radio>
            <el-radio v-model="ruleForm.isZF" label="1">反面</el-radio>
          </el-col>
        </el-form-item> -->

        <el-form-item label="是否启用" prop="isUse">
          <el-col :span="12">
            <el-radio v-model="ruleForm.isUse" label="0">是</el-radio>
            <el-radio v-model="ruleForm.isUse" label="1">否</el-radio>
          </el-col>
        </el-form-item>

        <!-- <el-form-item label="是否全局默认">
          <el-col :span="12">
            <el-radio v-model="ruleForm.isGlobal" label="0">是</el-radio>
            <el-radio v-model="ruleForm.isGlobal" label="1">否</el-radio>
          </el-col>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import TreeCommon from "../components/TreeCommon.vue";
import { tool } from "../utils/tool";
import {
  fontManage,
  getFontNames,
  fontManage2,
  fontManage3,
} from "../api/first";
export default {
  components: {
    TreeCommon,
  },
  data() {
    return {
      tableHeight: 730,
      dialog: {
        title: "",
        dialogVisible: false,
      },
      rules:{
        fontName:[{ required: true, message: "字体名称不能为空", trigger: "blur" }],
        fontSize:[{ required: true, message: "字体大小不能为空", trigger: "blur" }],
        isUse:[{ required: true, message: "请选择是否启用", trigger: "blur" }],
      },
      ruleForm: {},
      tableData: [],
      logDetailsTableData: [],
      fontData: [],
    };
  },
  created() {
    this.getFont();
  },
  mounted() {
    this.initTableHeight();
  },
  methods: {
    parseTime(val) {
      let time = parseInt(val);
      return tool.parseTime(time);
    }, //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      // let height = document.documentElement.clientHeight;
      // let that = this;
      // // 一定要使用 nextTick 来改变height 不然不会起作用
      // //nav 45;
      // //padding:10
      // this.$nextTick(() => {
      //   // let tempHeight = height - 45 - 10 - 51;
      //   // let treeDemoHeight = tempHeight + 30;
      //   // that.$refs.downTree.style = "height:" + treeDemoHeight + "px";
      //   // this.tableHeight = tempHeight;
      // });
      fontManage2().then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.tableData = res.data;
        }
      });
    },

    getFont() {
      getFontNames().then((res) => {
        Object.keys(res).forEach((item) => {
          this.fontData = res[item];
        });
      });
    },
    handleAdd() {
      // this.ruleForm = {};
      this.dialog.title = "新增";
      this.dialog.dialogVisible = true;
    },
    handleClose() {
      this.dialog.dialogVisible = false;
      this.ruleForm = {};
      this.$refs.ruleForm.resetFields();
    },
    handleRemove(index, row) {
      fontManage3({id:row.id}).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: res.msg,
            type: "success",
          });
          
        } else {
           this.$message({
            message: res.msg,
            type: "success",
          });
        }
      });
      setTimeout(() => {
        this.initTableHeight();
        
      }, 500);
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.id) {
            fontManage(this.ruleForm).then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "修改成功",
                  type: "success",
                });
              } else {
                this.$message({
                  message: "修改失败",
                  type: "warning",
                });
              }
            });
          } else {
            fontManage(this.ruleForm).then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "添加成功",
                  type: "success",
                });
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            });
            this.dialog.dialogVisible = false;
            this.ruleForm = {};
            this.$refs.ruleForm.resetFields();
            setTimeout(() => {
              this.initTableHeight();
            }, 500);
          }
        }else {
          console.log("error submit!!");
          return false;
        }
      });

    },
    handleEdit(index, row) {
      // this.ruleForm = {};
      console.log(row);
      this.dialog.title = "编辑";
      this.dialog.dialogVisible = true;
      this.ruleForm = row;
    },
    cancel() {
      this.dialog.dialogVisible = false;
      this.ruleForm = {};
      this.$refs.ruleForm.resetFields();
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    nodeClick(data) {
      this.query.sblsh = null;
      this.query.pageNo = 0;
      this.query.type = data.type;
      //部门
      if (data.type == 0) {
        this.query.bmbm = data.id;
        this.query.name = data.name;
        this.query.itemCode = null;
        this.query.itemName = null;
        this.query.labelOne = "部门编码";
        this.query.labelTwo = "部门名称";
      }
      //事项
      if (data.type == 1) {
        this.query.bmbm = null;
        this.query.name = null;
        this.query.taskCode = data.id;
        this.query.itemName = data.name;
        this.query.labelOne = "事项编码";
        this.query.labelTwo = "事项名称";
      }
    },
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}
.el-row {
  margin: 0px;
  padding: 0px;
}
.down-tree {
  display: block;
  overflow-y: scroll;
}
</style>

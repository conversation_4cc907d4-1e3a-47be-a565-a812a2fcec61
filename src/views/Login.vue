<template>
  <div class="login-wrap"></div>
</template>

<script>
export default {
  components: {
    InFeedTreeCommon,
  },
};
</script>

<style scoped>
.login-wrap {
  position: relative;
  width: 100%;
  height: 200px;
  background-image: url(../assets/img/group8.png);
  background-size: 100%;
}
.ms-title {
  width: 100%;
  line-height: 50px;
  text-align: center;
  font-size: 20px;
  color: #fff;
  border-bottom: 1px solid #ddd;
}
.ms-login {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 350px;
  margin: -190px 0 0 -175px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
  overflow: hidden;
}
.ms-content {
  padding: 30px 30px;
}
.login-btn {
  text-align: center;
}
.login-wrap .title {
  position: absolute;
  color: white;
  width: 100%;
  text-align: center;
  top: 15%;
  font-size: 40px;
  font-weight: bolder;
}
.login-btn button {
  width: 100%;
  height: 36px;
  margin-bottom: 10px;
}
.login-tips {
  font-size: 12px;
  line-height: 30px;
  color: #fff;
}
.login-wrap .footer {
  position: absolute;
  bottom: 5%;
  color: white;
  text-align: center;
  width: 100%;
}

.topLine {
  position: absolute;
  margin: 0 auto;
  width: 500px;
  top: 22%;
  left: 45%;
  margin-top: -100px;
  margin-left: -150px;
  height: 1px;
  background: radial-gradient(#7395b9 12%, #324157 80%);
}
.bottomLine {
  position: absolute;
  margin: 0 auto;
  width: 500px;
  top: 34%;
  left: 45%;
  margin-top: -100px;
  margin-left: -150px;
  height: 1px;
  background: radial-gradient(#7395b9 12%, #324157 80%);
}
</style>

<template>
  <!-- xml管理 -->
  <div class="divBox">
    <div class="searchDiv">
      <el-form
        :inline="true"
        :model="query"
        class="demo-form-inline"
        size="small"
      >
        <el-form-item label="配置信息名称">
          <el-select v-model="query.type" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" @click="handleAddParent"
            >添加根节点
          </el-button>
          <el-button @click="toggleExpandAll">收起与展开</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row :gutter="25">
      <el-col :span="18">
        <el-table
          v-if="refreshTable"
          :default-expand-all="expends"
          :data="tableData"
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :height="tableHeight"
          :row-style="{ height: '0' }"
          :cell-style="{ padding: '3px' }"
          border
        >
          <el-table-column prop="pid" label="节点" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.pid == 0" size="small">根节点</el-tag>
              <el-tag v-if="scope.row.pid != 0" size="small" type="warning"
                >子节点</el-tag
              >
            </template>
          </el-table-column>

          <el-table-column
            prop="name"
            label="属性名称"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="xmlName"
            label="xml名称"
            align="center"
          ></el-table-column>
          <!-- <el-table-column
            prop="ythName"
            label="字段属性"
            align="center"
          ></el-table-column> -->
          <!-- <el-table-column
            prop="dataType"
            label="数据类型"
            align="center"
          ></el-table-column> -->
          <el-table-column
            prop="isRepeatable"
            label="是否可重复"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isRepeatable == 0">可重复</span>
              <span v-if="scope.row.isRepeatable == 1">不可重复</span>
            </template>
          </el-table-column>
          <el-table-column prop="isRequired" label="是否必须" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isRequired == 0">可选</span>
              <span v-if="scope.row.isRequired == 1">必须</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="elementClassification"
            label="元素分类"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="elementType"
            label="元素类型"
            align="center"
          ></el-table-column>
          <el-table-column prop="isUse" label="是否可用" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.pid == 0" size="small">已启用</el-tag>
              <el-tag v-if="scope.row.pid == 1" size="small" type="warning"
                >未启用</el-tag
              >
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-edit"
                @click="handleEdit(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                type="text"
                icon="el-icon-delete"
                v-if="scope.row.children.length == 0"
                @click="handleRemove(scope.$index, scope.row)"
                >删除</el-button
              >
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="handleAddChildren(scope.$index, scope.row)"
                >添加子节点</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog
      :title="dialog.title"
      :visible.sync="dialog.dialogVisible"
      :before-close="handleClose"
      width="800px"
      center
    >
      <el-form
        :inline="true"
        status-icon
        :rules="rules"
        ref="ruleForm"
        :model="ruleForm"
        size="small"
        label-width="100px"
        class="demo-ruleForm"
        center
      >
        <el-form-item label="属性名称" prop="name">
          <el-input
            v-model="ruleForm.name"
            placeholder="请输入属性名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="属性Xml" prop="xmlName">
          <el-input
            v-model="ruleForm.xmlName"
            placeholder="请输入属性Xml名称"
          ></el-input>
        </el-form-item>

        <el-form-item label="字段属性" prop="sbjcId">
          <el-select v-model="ruleForm.sbjcId" filterable placeholder="请选择">
            <el-option
              v-for="item in sbjcOptions"
              :key="item.id"
              :label="item.strValue + '(' + item.ythName + ')'"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否可重复" prop="isRepeatable">
          <el-select v-model="ruleForm.isRepeatable" placeholder="请选择">
            <el-option label="可重复" value="0"></el-option>
            <el-option label="不可重复" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否必须" prop="isRequired">
          <el-select v-model="ruleForm.isRequired" placeholder="请选择">
            <el-option label="必须" value="0"></el-option>
            <el-option label="可选" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="元素分类" prop="elementClassification">
          <el-select
            v-model="ruleForm.elementClassification"
            placeholder="请选择"
          >
            <el-option label="业务类" value="业务类"></el-option>
            <el-option label="档案类" value="档案类"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="元素类型" prop="elementType">
          <el-select v-model="ruleForm.elementType" placeholder="请选择">
            <el-option label="简单型" value="简单型"></el-option>
            <el-option label="容器型" value="容器型"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用" prop="isUse">
          <el-radio-group v-model="ruleForm.isUse">
            <el-radio label="0">启用</el-radio>
            <el-radio label="1">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog.dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getAll, findSbjcAll, save, remove } from "../api/archivesAttributeApi";
export default {
  data() {
    return {
      total: 0,
      tableHeight: 730,
      tableData: [],
      query: {
        pageNo: 0,
        pageSize: 100,
        type: "pz",
      },
      refreshTable: true,
      expends: false, //表格展开与收起
      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      ruleForm: {
        type: "",
      },
      options: [
        { label: "归档配置信息", value: "pz" },
        { label: "基本信息", value: "basic" },
        { label: "办理流程信息", value: "gc" },
      ],
      rules: {
        name: [{ required: true, message: "请输入属性名称", trigger: "blur" }],
        xmlName: [
          { required: true, message: "请输入xml名称", trigger: "blur" },
        ],
        fieldName: [{ required: true, message: "请选择字段", trigger: "blur" }],
        isRepeatable: [
          { required: true, message: "请选择是否可重复", trigger: "blur" },
        ],
        elementClassification: [
          { required: true, message: "请选择元素分类", trigger: "blur" },
        ],
        elementType: [
          { required: true, message: "请选择元素类型", trigger: "blur" },
        ],
        isUse: [{ required: true, message: "请选择状态", trigger: "blur" }],
        sbjcId: [
          { required: true, message: "请选择字段属性", trigger: "blur" },
        ],
        isRequired: [
          { required: true, message: "请选择是否必须", trigger: "blur" },
        ],
      },
      sbjcOptions: [],
    };
  },
  methods: {
    toggleExpandAll() {
      this.refreshTable = false;
      this.expends = !this.expends;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    handleAddParent() {
      this.ruleForm = {};
      this.dialog.title = "新增父级节点";
      this.dialog.dialogVisible = true;
      this.ruleForm.pid = "0";
      this.findSelect();
    },
    findSelect() {
      findSbjcAll({ type: this.query.type }).then((res) => {
        this.sbjcOptions = res.data;
      });
    },
    handleEdit(index, row) {
      this.dialog.title = "编辑父级节点";
      this.dialog.dialogVisible = true;
      this.findSelect();
      this.ruleForm = row;
    },
    handleAddChildren(index, row) {
      this.ruleForm = {};
      this.dialog.title = "新增子级节点";
      this.dialog.dialogVisible = true;
      this.findSelect();
      this.ruleForm.pid = row.id; //设置节点
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该级点, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          remove({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.$message.success("删除成功");
              this.getData();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => {});
    },
    handleClose(done) {
      done();
    },
    search() {
      this.getData();
    },
    cancel() {
      this.dialog.dialogVisible = false;
      this.getData();
    },
    getSbjcAll() {
      findSbjcAll(this.query).then((res) => {
        console.log(res);
      });
    },
    save(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          this.ruleForm.type = this.query.type;
          save(this.ruleForm).then((a) => {
            if (a.code == 200) {
              this.$message.success("保存成功");
              this.cancel();
            } else {
              this.$message.error("保存失败");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getData() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      getAll(this.query)
        .then((res) => {
          this.tableData = res.data;
          this.total = res.data.totalCount;
          //成功回调函数停止加载
          loading.close();
        })
        .catch((err) => {});
    },
    handleSizeLogChange(val) {
      this.logQuery.pageSize = val;
      this.logQuery.pageNoTemp = 1;
      this.logQuery.pageNo = 0;
      this.getData();
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.getData();
    },
  },
  created() {
    this.getData();
  },
};
</script>

<style scoped>
.box {
  padding: 10px;
}
.searchDiv {
  margin-top: 10px;
}
.ant-table-tbody > tr > td {
  padding: 0px !important;
}
</style>

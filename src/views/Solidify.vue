<template>
  <!-- 期限 -->
  <div>
    <div class="searchDiv">
      <el-form
        :inline="true"
        :model="query"
        class="demo-form-inline"
        size="small"
      >
        <el-form-item label="固化文件名称">
          <el-input
            v-model="query.fileName"
            placeholder="固化文件名称"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" style="width: 100%" height="700" border>
      <el-table-column
        prop="fileName"
        label="文件名称"
        align="center"
      ></el-table-column>
      <!-- <el-table-column
        prop="zipName"
        label="所属文件夹"
        align="center"
      ></el-table-column> -->
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            icon="el-icon-delete"
            @click="handleRemove(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="dialog.title"
      :visible.sync="dialog.show"
      width="30%"
      :before-close="handleClose"
      center
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="ruleForm.fileName"></el-input>
        </el-form-item>
        <el-form-item label="文件夹所属" prop="zipName">
          <el-select
            v-model="ruleForm.zipName"
            clearable
            filterable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in randeId"
              :key="item.dvalue"
              :label="item.dname"
              :value="item.dvalue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { materialsBillApi } from "../api/materialsBill";
import { dataSetApi } from "../api/dataSet";
export default {
  data() {
    return {
      query: {
        pageNo: 0,
        pageSize: 100,
      },
      dialog: {
        title: "新增",
        show: false,
      },
      rules: {
        fileName: [
          { required: true, message: "固化文件名称不能为空", trigger: "blur" },
        ],
        zipName: [
          { required: true, message: "文件夹所属不能为空", trigger: "blur" },
        ],
      },
      randeId: [],
      ruleForm: {},
      tableData: [],
    };
  },
  created() {
    this.getData();
    this.initData();
  },
  methods: {
    initData() {
      dataSetApi.getData({ code: "source" }).then((a) => {
        this.randeId = a.data;
      });
    },
    handleSizeChange(val) {
      this.query.pageSize = val;

      this.query.pageNoTemp = 1;

      this.query.pageNo = 0;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNoTemp = val;

      this.query.pageNo = val - 1;
      this.getData();
    },
    close() {
      this.dialog.show = false;
      this.ruleForm = {};
    },
    handleEdit(index, row) {
      this.dialog.title = "编辑";
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.dialog.show = true;
    },
    handleAdd() {
      this.dialog.title = "新增";
      this.dialog.show = true;
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          materialsBillApi.solidifySave(this.ruleForm).then((a) => {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
            this.query.pageNo = 0;
            this.getData();
            this.ruleForm = {};
            this.dialog.show = false;
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          materialsBillApi.solidifyRemove({ id: row.id }).then((a) => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.query.pageNo = 0;
            this.getData();
          });
        })
        .catch(() => {});
    },
    search() {
      this.query.pageNo = 0;
      this.getData();
    },
    getData() {
      materialsBillApi.getSolidifyList(this.query).then((a) => {
        this.tableData = a.data;
      });
    },
  },
};
</script>
<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="first"></el-tab-pane>
      <el-tab-pane label="流程信息" name="second"></el-tab-pane>
      <el-tab-pane label="归档信息" name="third">
        <GDForm :form="GDForm"/>
      </el-tab-pane>
      <el-tab-pane label="电子文件信息" name="fourth">
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
import SLForm from "../components/BasicForm.vue";
import GCForm from "../components/GCForm.vue";
import BJForm from "../components/BJForm.vue";
import LQForm from "../components/LQForm.vue";
import GDForm from "../components/GDForm.vue";
import {getGdData, addCustomStting, getBjData} from '../api/first'
import {dictComputed, isTown, resultCetrType} from "@/dict/dictionaries";

export default {
  components: {
    SLForm,
    GCForm,
    BJForm,
    LQForm,
    GDForm,
  },
  data() {
    return {
      activeName: "third",
      GDForm: {},
    };
  },
  methods: {
    getData() {
      getGdData({sblsh: localStorage.getItem("sblsh"), name: 'pz'}).then((res) => {
        if (res.code == 200) {
          this.GDForm = res.data;
          this.GDForm.className = res.data.classificationType.name
          this.GDForm.createDate = res.data.classificationType.createDate
          this.GDForm.retenName = res.data.retentionPeriod.name
          this.GDForm.year = res.data.year.substr(0, 4)
        }
      });
      //查询题名和文号
      getBjData({sblsh: localStorage.getItem("sblsh")}).then((res) => {
        if (res.code == 200) {
          this.GDForm.tiMing = res.data.tiMing;
          this.GDForm.wenHao = res.data.wenHao;
        }
      });
    },
    handleClick(tab, event) {
      switch (tab.name) {
        case "second":
          this.$router.push({
            path: "./second",
            query: {sblsh: localStorage.getItem("sblsh")},
          });
          break;
        case "third":
          this.$router.push({
            path: "./third",
            query: {sblsh: localStorage.getItem("sblsh")},
          });
          break;
        case "fourth":
          this.$router.push({
            path: "./fourth",
            query: {sblsh: localStorage.getItem("sblsh")},
          });
          break;
        case "first":
          this.$router.push({
            path: "./first",
            query: {sblsh: localStorage.getItem("sblsh")},
          });
          break;
        case "return":
          this.$router.push({
            path: "./metadataManagement",
            // query: { sblsh: localStorage.getItem("sblsh") },
          });
          break;
      }
    },
    tui() {
      this.$router.push({path: "./metadataManagement"});
      // this.$router.back()
    },
    save() {
      this.GDForm.sblsh = localStorage.getItem("sblsh")
      addCustomStting(this.GDForm).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: "保存成功",
            type: "success",
          });
        } else {
          this.$message({
            message: "保存失败",
            type: "warning",
          });
        }
      });

    },
    cancel() {
    }
  },
  mounted() {
    this.getData()
  }
};
</script>
<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}

::v-deep #tab-return {
  margin-left: 1200px;
  /* border: #3a8ee6 solid 1px; */
  padding: 5px 15px;
  align-content: center;
  margin-top: 2px;
  border-radius: 5px;
  text-align: center;
  line-height: 20px;
  font-size: medium;
}
</style>
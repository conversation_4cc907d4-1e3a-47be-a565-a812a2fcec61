<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="真实性" name="first">
      <el-form>
      <div>
          <div class="title">电子文件来源真实性</div>
          <!-- `checked` 为 true 或 false -->
            <el-checkbox v-model="checked" style="color: #1a6bac">固化信息有效性检测
            </el-checkbox>
            <span style="margin-left: 5px;font-size: 5px">对归档电子文件中包含的数字摘要、电子签名、电子印章、时间戳等技术措施的固化信息的有效性进行验证</span>
          </div>
        <div class="title">电子文件元数据准确性</div>
      </el-form>
      </el-tab-pane>
      <el-tab-pane label="完整性" name="second">配置管理</el-tab-pane>
      <el-tab-pane label="可用性" name="third">
        <div class="title">电子文件来源真实性</div>
          <!-- `checked` 为 true 或 false -->
        <el-form >
          <el-row :gutter="20">
            <el-form-item>
              <el-col :span="24">
            <el-checkbox v-model="checked" style="color: #1a6bac">电子文件元数据可用性
            </el-checkbox>
            <span style="margin-left: 5px;font-size: 5px">检测电子文件元数据是否可以被正常访问。</span>
              </el-col>
            </el-form-item>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="安全性" name="fourth">
        <el-form >
          <el-row :gutter="20">
          <div class="title">归档信息包检测</div>
          <el-form-item>
            <el-col :span="24">
            <el-checkbox v-model="checked" style="color: #1a6bac">归档信息包病毒检测
            </el-checkbox>
            <span style="margin-left: 5px;font-size: 5px">检测系统环境中是否安装杀毒软件；检测归档信息包是否包含计算机病毒。</span>
            </el-col>
          </el-form-item>

          <el-form-item>
            <el-col :span="24">
            <el-checkbox v-model="checked" style="color: #1a6bac">归档载体安全性检测
            </el-checkbox>
            <span style="margin-left: 5px;font-size: 5px">检测载体内是否含有非归档文件；通过外观、读取情况等判定载体是否安全、可靠；针对光盘，
    检测其是否符合 DA/T38-2008 的有关要求。</span>
            </el-col>
          </el-form-item>
          </el-row>
        </el-form>
      </el-tab-pane>
  </el-tabs>
</template>
<script>
export default {
  data() {
    return {
      activeName: 'first',
      checked:false
    };
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
};
</script>
<style scoped>
.title {
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 10px;
  padding-bottom: 10px;
  font-size: 16px;
  margin-left: 10px;
}
</style>
<template>
  <div class="divBox">
    <div class="searchDiv1">
      <el-form :inline="true" :model="query" class="demo-form-inline" size="small">
        <el-form-item label="部门编码">
          <el-input v-model="query.bmbm" placeholder="部门编码"></el-input>
        </el-form-item>
        <el-form-item label="部门名称">
          <el-input v-model="query.departName" placeholder="部门名称"></el-input>
        </el-form-item>
        <el-form-item label="全宗号">
          <el-input v-model="query.qzh" placeholder="全宗号"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="handleAdd">添加</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="warning" plain @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table ref="tables" :data="tableData" class="tableClass" :header-cell-style="{ background: '#F6F7FA' }"
      :height="tableHeight" stripe>
      <el-table-column prop="bmbm" label="序号" align="center">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="bmbm" label="部门编码" align="center"></el-table-column>
      <el-table-column prop="name" label="部门名称" align="center"></el-table-column>
      <el-table-column prop="qzh" label="全宗号" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleEdit(scope.$index, scope.row)">编辑
          </el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleRemove(scope.$index, scope.row)">删除
          </el-button>
          <el-button type="text" icon="el-icon-document-copy" @click="handleViewItemCode(scope.$index, scope.row)">事项
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:currentPage="query.pageSize" :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper" :total="query.total" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" style="float: right;margin-top: 10px;"/>
    <!-- 部门编辑 -->
    <el-dialog :title="dialog.title" width="35%" :visible.sync="dialog.dialogVisible" :before-close="handleClose" center>
      <el-form center :model="ruleForm" status-icon :rules="rules" ref="ruleForm" size="small" label-width="100px"
        class="demo-ruleForm">
        <el-form-item label="部门编码" prop="bmbm">
          <el-input v-model="ruleForm.bmbm" autocomplete="off">
            <!-- <template slot="append">
                             <el-button type="primary" @click="handleSync">同步</el-button>
                         </template> -->
          </el-input>
        </el-form-item>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="ruleForm.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="全宗号" prop="qzh">
          <el-input v-model="ruleForm.qzh"></el-input>
        </el-form-item>

        <el-form-item label="目录配置">
          <el-radio-group v-model="ruleForm.folderType" @change="zipPackagesConfigChange">
            <el-radio :label="0">默认</el-radio>
            <el-radio :label="1">自定义</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-tree v-show="zipPackagesDataOpen" ref="tree" :data="zipPackagesData" show-checkbox highlight-current
            accordion node-key="id" :props="defaultProps">
          </el-tree>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save('ruleForm')">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </span>
    </el-dialog>
    <!-- 事项列表 -->
    <el-dialog :title="itemCodeDialog.title" fullscreen :visible.sync="itemCodeDialog.dialogVisible"
      :close-on-click-modal="true" :before-close="handleClose"  width="75%">
      <itemCode v-if="itemCodeDialog.dialogVisible" :bmName="bmName" :bmbm="bmbm"></itemCode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import itemCode from "../components/ItemCode.vue";
import { departApi } from "../api/department";
import { archivesTypeApi } from "../api/archivesType";
import { zipPackageApi } from "../api/zipPackage";
import { itemCodeApi } from "@/api/itemCode";

export default {
  components: {
    itemCode,
  },
  data() {
    return {
      folder: 'iconFolder',    //显示文件夹  下面有css样式
      file: 'iconFile',        //显示文件
      tableHeight: 730,
      bmbm: null,
      bmName: null,
      query: {
        pageNo: 0,
        pageSize: 10,
        total: 0,
      },
      itemCodeDialog: {
        title: "事项",
        dialogVisible: false,
      },
      dialog: {
        title: "新增",
        dialogVisible: false,
      },
      rules: {
        bmbm: [{ required: true, message: "请输入部门编码", trigger: "blur" }],
        name: [{ required: true, message: "请输入部门名称", trigger: "blur" }],
        qzh: [{ required: true, message: "请输入全宗号", trigger: "blur" }],
      },
      ruleForm: {
        folderType: 0
      },
      tableData: [],
      zipPackages: [],//文件目录
      archivesTypes: [],//档案类别
      archivesTypesId: "",//档案类别id
      zipPackagesData: [],
      zipPackagesDataOpen: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      zipPackagesConfig: "0"
    };
  },
  created() {
    this.getData();
    this.getBaseData()
  },
  mounted() {
    this.initTableHeight();
  },
  methods: {
    getBaseData() {
      Promise.all([
        new Promise((re, rp) => {
          zipPackageApi.getAll().then(res => {
            re(res)
          })
        })
      ]).then(res => {
        /*this.zipPackages = res[0].data
        this.archivesTypes = res[1].data*/
        this.zipPackagesData = res[0]
      })

    },
    archivesTypesChange(val) {
      this.archivesTypesId = val
      this.getBaseData()
    },
    zipPackagesConfigChange(val) {
      this.zipPackagesDataOpen = false
      if (val === 1) {
        this.zipPackagesDataOpen = true
        this.$nextTick(() => {
          let that = this;
          var array = Array.from(that.zipPackages)
          console.log(this.zipPackagesData)
          array.forEach((v, i) => {

            var node = that.$refs.tree.getNode(v.id)
            if (!node.isLeaf) {
              that.$refs.tree.setChecked(v.id, true, false)

            }
            // v.list.forEach((a, i) => {
            //   that.$refs.tree.setChecked(a, true)
            // })
          })
          let nodes = that.$refs.tree.getCheckedNodes()
          nodes.forEach((a, i) => {
            a.children.forEach(item => {
              that.$refs.tree.setChecked(item.id, true)
            })
          })
          // console.log(that.$refs.tree.getCheckedNodes())
        });
      }
    },
    checked(id, data, newArr) {
      var This = this;
      newArr = [];
      //循环判断id是否相同、是否有子级
      data.forEach((item) => {
        if (item.id == id) {
          //id相同，且没有子级的时候，将值赋值给tree
          if (item.children.length == 0) {
            This.checkedKeys.push(item.id);
          }
        } else {
          //否则将继续循环判断
          if (item.hasOwnProperty("children")) {
            if (item.children.length != 0) {
              This.checked(id, item.children, newArr);
            }
          }
        }
      });
    },
    test(data, node, obj) {
      let checkNodes = this.$refs.tree.getCheckedNodes(false, true).map(i => i.id)
    },
    //初始化表格高度
    initTableHeight() {
      //获取窗口高度
      let height = document.documentElement.clientHeight;
      // 一定要使用 nextTick 来改变height 不然不会起作用
      //nav 45;
      //padding:10
      this.$nextTick(() => {
        let tempHeight = height - 45 - 50 - 51;
        this.tableHeight = tempHeight;
      });
    },
    handleAdd() {
      this.dialog.title = "添加";
      this.ruleForm.folderType = 0;
      this.zipPackagesDataOpen = false;
      this.dialog.dialogVisible = true;
      if (this.$refs.tree.getCheckedNodes().length > 0) {
        this.$nextTick(() => {
          this.$refs.tree.setCheckedKeys([])
        })
      }
      this.ruleForm = JSON.parse(JSON.stringify(this.ruleForm));
    },
    search() {
      this.getData();
    },
    handleSizeChange(val) {
      this.query.pageNo = 0;
      this.query.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.query.pageNo = val - 1;
      this.getData();
    },
    handleEdit(index, row) {
      this.dialog.title = "编辑";
      this.ruleForm = JSON.parse(JSON.stringify(row));
      this.archivesTypesId = row.archivesTypeId
      this.dialog.dialogVisible = true;
      departApi.getAllByUnitsId({ unitsId: row.id }).then(res => {
        this.zipPackages = res
        this.zipPackagesConfigChange(row.folderType)
      })
    },
    handleSync() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Loading", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(0, 0, 0, 0.7)", //遮罩层颜色
        target: document.querySelector("#table"), //loadin覆盖的dom元素节点
      });
      departApi.syncData({ bmbm: this.ruleForm.bmbm, name: this.ruleForm.name })
        .then((a) => {
          this.form = a.data[0];
          loading.close();
        })
        .catch((a) => {
          loading.close();
        });
    },
    handleClose(done) {
      this.form = {};
      this.ruleForm = {};
      done();
    },
    handleViewItemCode(index, row) {
      this.bmName = row.name;
      this.bmbm = row.bmbm;
      this.itemCodeDialog.dialogVisible = true;
    },
    handleRemove(index, row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          departApi.remove({ id: row.id }).then((a) => {
            if (a.code == 200) {
              this.$message.success("删除成功");
              this.query.pageNo = 0;
              this.getData();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => {
        });
    },
    cancel() {
      this.form = {};
      this.ruleForm = {};
      this.dialog.dialogVisible = false;
      this.itemCodeDialog.dialogVisible = false;
    },
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.folderType === 1) {
            let nodes = this.$refs.tree.getCheckedNodes()
            this.zipPackagesData.map(item => {
              item.children.map(child => {
                let exists = nodes.filter(sel => sel.id == child.id);
                if (exists.length > 0) {
                  item.select = true
                  child.select = true
                }
              })
            })
            let arr = []
            this.zipPackagesData.map(item => {
              if (item.select) {
                let node = {}
                node.id = item.id
                node.list = []
                item.children.map(child => {
                  if (child.select) {
                    node.list.push(child.id)
                  }
                })
                arr.push(node)
              }
            })
            this.ruleForm.idList = arr;
          }
          departApi.save(this.ruleForm).then((a) => {
            if (a.code === 200) {
              this.$message.success("保存成功");
              this.cancel();
              this.getData();
            } else {
              this.$message.error("保存失败");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getData() {
      departApi.page(this.query).then((a) => {
        if (a.code == 200) {
          this.tableData = a.data;
          this.query.total = a.totalCount;
        }
      });
    },
    //excel导出
    handleExport() {
      this.$confirm("此操作将导出事项列表, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        itemCodeApi.exportItemCodeList().then(data => {
          let url = window.URL.createObjectURL(new Blob([data]));
          let link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', 'ItemCode.xlsx');
          document.body.appendChild(link);
          link.click();
        })
      })
    }
  },
};
</script>
<style scoped>
.box {
  padding: 10px;
}

/* .searchDiv {
  margin-top: 10px;
} */
.divBox{
  /* padding-top: 10px; */
  padding: 15px;
  box-sizing: border-box;
}
.iconFolder::before {
  /* background-color: aqua; */
  content: '';
  /* float: left; */
  display: inline-block;
  width: 15px;
  height: 15px;
  /* border: 1px solid#000; */
  background: url(../assets/img/folder.png) no-repeat;
  background-size: 15px;
}

.iconFile::before {
  /* background-color: aqua; */
  content: '';
  /* float: left; */
  display: inline-block;
  width: 15px;
  height: 15px;
  /* border: 1px solid#000; */
  background: url(../assets/img/folder.png) no-repeat;
  background-size: 15px;
}

.tableClass {
  width: 100%;
  /* padding: 0 20px; */
}

</style>

<template>

</template>

<script>
import {getToken} from '@/api/login.js'

export default {
  name: "redirectPage",
  data() {
    return {
      code: '',
      url: ''
    }
  },
  created() {

    this.code = this.$route.query.code;
    // this.url = this.$route.query.url;
    // this.url = param.get("url");
    // this.code = param.get("code");
    // let paramInfo = location.href.split("?")[1];
    // let param = new URLSearchParams("?" + paramInfo);
    console.log(window.parent.document.getElementById("mainFrame"))
    let paramInfo = window.parent.document.getElementById("mainFrame").src.split("?")[1];
    let param = new URLSearchParams("?" + paramInfo);
    this.url = param.get("url")
    console.log(this.url, this.code)

    /* this.$router.push({
       path: this.url
     })*/
    // getToken({code: this.code}).then(res => {
    //   this.$router.push({
    //     path: this.url
    //   })
    // })
  }
}
</script>

<style scoped>

</style>
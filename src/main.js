import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

import loading from "./components/Loading";
//挂载字典
import {getDict, getDictList} from '@/utils/getDict'

Vue.prototype.$getDict = getDict
Vue.prototype.$getDictList = getDictList


Vue.use(loading);
Vue.config.productionTip = false;
import "./assets/css/public.css";
import axios from "axios";
import qs from "qs";
import '@/utils/dialogdrag.js' // 引入移动事件（el-drag-dialog.js的内容为上面的代码块）
Vue.prototype.axios = axios;
Vue.prototype.qs = qs;
// Element.Dialog.props.closeOnClickModal.default = true;
Vue.use(ElementUI);
Vue.use(router);
// 弹窗默认点击遮罩改为不关闭 为了防止和拖拽冲突 ，这句需要放在use ElementUI之前（也可以不加这句，自己测试区别）
new Vue({
  router,
  store,
  render: function (h) {
    return h(App);
  },
}).$mount("#app");

{"name": "archives-font", "version": "0.1.0", "private": true, "scripts": {"serve": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --mode development", "build": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode production", "build-test": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode test", "postinstall": "patch-package"}, "dependencies": {"axios": "^0.24.0", "caniuse-lite": "^1.0.30001502", "element-ui": "^2.15.13", "jquery": "^3.6.0", "leader-line": "^1.0.7", "patch-package": "^6.4.7", "qs": "^6.11.0", "sass-loader": "^8.0.0", "vue": "^2.6.11", "vue-loader": "^17.0.1", "vue-pdf": "^4.3.0", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "skeleton-loader": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
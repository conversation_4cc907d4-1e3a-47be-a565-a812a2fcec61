const path = require("path");
module.exports = {
    publicPath: process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test' ? '/business-web/' : '/',
    outputDir: './business-web/',
    assetsDir: 'static',
    productionSourceMap: true,
    parallel: true,
    configureWebpack: (config) => {
        config.module.rules.push({
            test: path.resolve(__dirname, "node_modules/leader-line/"),
            use: [
                {
                    loader: "skeleton-loader",
                    options: {
                        procedure: (content) => `${content}export default LeaderLine`,
                    },
                },
            ],
        });
    },
   /* devServer: {
        port: 80,
        open: true,
        disableHostCheck: true,
        proxy: {
            '/api': {
                ws: false,
                timeout: 3600000,
                target: 'http://localhost:8088', // 设置被替换代理的目的域名与端口
                secure: false, // 设置是否允许 https接口
                changeOrigin: true, // 设置是否允许接口跨域
                pathRewrite: {
                    '^/api': '' // 设置路径重写的代理名称，这里用代理名称代替target里面的 'http://localhost:8002' 地址
                }
            }
        }
    }*/
};
